// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  build: {
    transpile: ["trpc-nuxt"],
  },
  typescript: {
    shim: false,
    tsConfig: {
      compilerOptions: {
        paths: {
          "~/schemas/*": ["./schemas/*"],
        },
      },
    },
  },

  hooks: {
    ready: () => {
      console.log("ready");
    },
    "nitro:init": () => {
      console.log("nitro:init");
    },
  },

  modules: [
    // "@element-plus/nuxt"
    "@prisma/nuxt",
    // "@sidebase/nuxt-session", // 已移除
    "@ant-design-vue/nuxt",
  ],

  imports: {
    dirs: ["types", "schemas"],
  },

  prisma: {
    installStudio: false,
    installClient: true,
  },

  // session配置已移除

  antd: {
    extractStyle: true,
  },

  vite: {
    plugins: [],
    resolve: {
      alias: {
        ".prisma/client/index-browser":
          "./node_modules/.prisma/client/index-browser.js",
      },
    },
  },

  //运行时全局变量
  runtimeConfig: {
    //只可以在服务端
    isServer: true,
    public: {
      //服务端客户端都可以使用
      baseURL: "",
      tableSize: 10,
      apiUrl: process.env.NODE_ENV === "production" ? "" : "",
    },
  },

  compatibilityDate: "2025-01-08",

  devtools: {
    enabled: true,
  },
  devServer: {
    host: "0.0.0.0",
    port: 3000,
  },
  nitro: {
    externals: {
      inline: ["@prisma/client"],
    },
  },
});
