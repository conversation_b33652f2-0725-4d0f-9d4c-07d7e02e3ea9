<template>
  <a-card title="物料清单管理-新建">
    <template #extra>
      <div>
        <a-button danger @click="exit()">退出</a-button>
      </div>
    </template>
    <a-steps :current="current" :items="items"></a-steps>
    <!-- <a-divider orientation="left">{{ items[current].title }}</a-divider> -->
    <a-form layout="vertical" :model="formState" ref="formEl" :rules="rules">
      <div class="steps-footer">
        <a-button v-if="current > 0" @click="prev">上一步</a-button>
        <a-button type="primary" @click="next">下一步</a-button>
      </div>
    </a-form>
  </a-card>
</template>
<script setup lang="ts">
  import type { Rule, Rules } from "async-validator";
  import type { RuleObject } from "ant-design-vue/es/form";
  const current = ref(0);
  const items = [
    { title: "基本信息", description: "填写物料清单基本信息" },
    { title: "物料清单", description: "填写物料清单物料" },
    { title: "版本", description: "填写物料清单版本" },
  ];
  const formState = ref<API.BomCreateInput>({
    title: "",
    materials: [] as { id: number; quantity: number }[],
    product_sku_id: 0,
    version: "",
    description: "",
  });
  const rules = reactive(formRules.addBom) as Record<string, RuleObject[]>;
  const exit = () => {
    navigateTo("/storemanage/admin/bom");
  };
  const prev = () => {
    current.value = current.value - 1;
  };
  const next = () => {
    current.value = current.value + 1;
  };
</script>
<style scoped>
  .steps-content {
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding: 16px;
  }
  .steps-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
</style>
