<template>
  <NuxtLayout>
    <template #header>欢迎使用Uarlab管理系统</template>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon235499s.jpg" />
      </template>
      <a-card-meta title="系统管理"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon240601s.jpg" />
      </template>
      <a-card-meta title="仓库&采购管理"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon596601s.jpg" />
      </template>
      <a-card-meta title="销售管理"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon232431s.jpg" />
      </template>
      <a-card-meta title="财务管理(新版)"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon281598s.jpg" />
      </template>
      <a-card-meta title="供货单位"></a-card-meta>
    </a-card>
  </NuxtLayout>
</template>
<script setup lang="ts">
  definePageMeta({
    alias: "login",
    layout: "manage-login",
  });
  const { $emitter } = useNuxtApp();

  const showLogin = () => {
    $emitter.emit("changeLoginBoxStatus", true);
  };

  const goHome = async () => {
    const user = await useAuth().getUser();
    if (user) {
      navigateTo("/storemanage/admin");
    }
  };

  $emitter.on("loginSuccessed", (user: API.LoginReturnType["data"]) => {
    goHome();
  });
</script>
