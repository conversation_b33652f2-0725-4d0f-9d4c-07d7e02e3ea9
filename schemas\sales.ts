import { z } from "zod";
import { baseTableSchema } from "./basetable";

// 销售出库记录查询schema
export const salesOutRecordQuerySchema = baseTableSchema.extend({
  customer_id: z.number().optional(),
  materiel_id: z.number().optional(),
  warehouse_id: z.number().optional(),
  order_no: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});
export type SalesOutRecordQueryInput = z.infer<typeof salesOutRecordQuerySchema>;

// 销售出库记录创建schema
export const salesOutRecordCreateSchema = z.object({
  customer_id: z.number(),
  materiel_id: z.number(),
  warehouse_id: z.number(),
  batch_no: z.string(),
  quantity: z.number().positive("数量必须大于0"),
  order_no: z.string(),
  note: z.string().optional(),
});
export type SalesOutRecordCreateInput = z.infer<typeof salesOutRecordCreateSchema>;
