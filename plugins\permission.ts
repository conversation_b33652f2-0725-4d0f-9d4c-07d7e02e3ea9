export default defineNuxtPlugin((nuxtApp) => {
  const { hasPermission } = usePermission();
  // 注册自定义指令
  nuxtApp.vueApp.directive("permission", {
    // beforeMount(el: HTMLElement, binding) {
    //   if (!hasPermission(binding.value)) {
    //     el.parentNode?.removeChild(el);
    //   }
    // },
    mounted(el: HTMLElement, binding) {
      if (!hasPermission(binding.value)) {
        el.parentNode?.removeChild(el);
      }
    },
  });
});
