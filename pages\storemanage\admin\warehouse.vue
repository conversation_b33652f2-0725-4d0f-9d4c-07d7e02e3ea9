<template>
  <a-card title="仓库管理">
    <template #extra>
      <a-button @click="handleAddWarehouse"> <plus-outlined /> 添加 </a-button>
    </template>
    <ui-storemanage-table
      :columns="columns"
      :query="useApiFetch.queryWarehouseList"
      ref="tableRef"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="handleEditWarehouse(record)"
            >编辑</a-button
          >
        </template>
      </template>
    </ui-storemanage-table>
  </a-card>
  <a-modal v-model:visible="visible" title="添加仓库" @ok="save()">
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="仓库名称" name="name">
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item label="仓库地址" name="address">
        <a-input v-model:value="formState.address" />
      </a-form-item>
      <a-form-item label="锁定" name="lock">
        <a-switch v-model:checked="formState.lock" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
  const formRef = ref();
  const visible = ref(false);
  const formState = reactive({
    id: undefined,
    name: "",
    address: "",
    lock: false,
  });
  const editFlag = ref(false);
  const handleAddWarehouse = () => {
    console.log("添加仓库");
    visible.value = true;
    editFlag.value = false;
  };
  const handleEditWarehouse = (record: any) => {
    editFlag.value = true;
    const data = deepClone(record);
    formState.id = data.id;
    formState.name = data.name;
    formState.address = data.address;
    formState.lock = data.lock;
    visible.value = true;
  };
  const save = async () => {
    console.log("保存仓库", formState);
    if (editFlag.value) {
      await useApiFetch.updateWarehouse(formState);
    } else {
      await useApiFetch.addWarehouse(formState);
    }
    formRef.value.resetFields();
    visible.value = false;
    tableRef.value.query();
  };
  const columns = [
    { title: "仓库名称", dataIndex: "name", key: "name" },
    { title: "仓库地址", dataIndex: "address", key: "address" },
    { title: "锁定", dataIndex: "lock", key: "lock" },
    { title: "操作", key: "action" },
  ];
  const tableRef = ref();
</script>
