/*
  Warnings:

  - You are about to alter the column `citycode` on the `Supplier` table. The data in that column could be lost. The data in that column will be cast from `<PERSON>r(6)` to `<PERSON><PERSON>`.
  - You are about to drop the column `role` on the `User` table. All the data in the column will be lost.
  - You are about to drop the `Auth` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Customer` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `InvoiceType` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Paper` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Product` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Purchase` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Saleorder` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `customer_invoiceinfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `customer_receivinginfo` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `product_sku` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `product_type` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `purchase_list` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `saleorder_invoice` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `saleorder_list` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `user_auth` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `warehouse_stock` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `warehouse_stock_in` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `warehouse_stock_out` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `contactName` to the `Supplier` table without a default value. This is not possible if the table is not empty.
  - Added the required column `contactPhone` to the `Supplier` table without a default value. This is not possible if the table is not empty.
  - Added the required column `email` to the `Supplier` table without a default value. This is not possible if the table is not empty.
  - Added the required column `role_id` to the `User` table without a default value. This is not possible if the table is not empty.
  - Made the column `email` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `contactTel` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `Customer` DROP FOREIGN KEY `Customer_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `Paper` DROP FOREIGN KEY `Paper_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `Paper` DROP FOREIGN KEY `Paper_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `Product` DROP FOREIGN KEY `Product_type_id_fkey`;

-- DropForeignKey
ALTER TABLE `Product` DROP FOREIGN KEY `Product_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `Purchase` DROP FOREIGN KEY `Purchase_supplier_id_fkey`;

-- DropForeignKey
ALTER TABLE `Purchase` DROP FOREIGN KEY `Purchase_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `Saleorder` DROP FOREIGN KEY `Saleorder_customer_id_fkey`;

-- DropForeignKey
ALTER TABLE `Saleorder` DROP FOREIGN KEY `Saleorder_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `customer_invoiceinfo` DROP FOREIGN KEY `customer_invoiceinfo_customer_id_fkey`;

-- DropForeignKey
ALTER TABLE `customer_invoiceinfo` DROP FOREIGN KEY `customer_invoiceinfo_invoicetype_id_fkey`;

-- DropForeignKey
ALTER TABLE `customer_receivinginfo` DROP FOREIGN KEY `customer_receivinginfo_customer_id_fkey`;

-- DropForeignKey
ALTER TABLE `customer_receivinginfo` DROP FOREIGN KEY `customer_receivinginfo_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `product_sku` DROP FOREIGN KEY `product_sku_productId_fkey`;

-- DropForeignKey
ALTER TABLE `product_type` DROP FOREIGN KEY `product_type_last_type_id_fkey`;

-- DropForeignKey
ALTER TABLE `purchase_list` DROP FOREIGN KEY `purchase_list_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `purchase_list` DROP FOREIGN KEY `purchase_list_purchase_id_fkey`;

-- DropForeignKey
ALTER TABLE `saleorder_invoice` DROP FOREIGN KEY `saleorder_invoice_invoicetype_id_fkey`;

-- DropForeignKey
ALTER TABLE `saleorder_invoice` DROP FOREIGN KEY `saleorder_invoice_saleorder_id_fkey`;

-- DropForeignKey
ALTER TABLE `saleorder_invoice` DROP FOREIGN KEY `saleorder_invoice_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `saleorder_list` DROP FOREIGN KEY `saleorder_list_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `saleorder_list` DROP FOREIGN KEY `saleorder_list_saleorder_id_fkey`;

-- DropForeignKey
ALTER TABLE `user_auth` DROP FOREIGN KEY `user_auth_auth_id_fkey`;

-- DropForeignKey
ALTER TABLE `user_auth` DROP FOREIGN KEY `user_auth_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock` DROP FOREIGN KEY `warehouse_stock_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock` DROP FOREIGN KEY `warehouse_stock_warehouse_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_in` DROP FOREIGN KEY `warehouse_stock_in_createuser_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_in` DROP FOREIGN KEY `warehouse_stock_in_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_in` DROP FOREIGN KEY `warehouse_stock_in_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_out` DROP FOREIGN KEY `warehouse_stock_out_createuser_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_out` DROP FOREIGN KEY `warehouse_stock_out_product_sku_id_fkey`;

-- DropForeignKey
ALTER TABLE `warehouse_stock_out` DROP FOREIGN KEY `warehouse_stock_out_user_id_fkey`;

-- DropIndex
DROP INDEX `User_code_key` ON `User`;

-- AlterTable
ALTER TABLE `Supplier` ADD COLUMN `contactName` VARCHAR(255) NOT NULL,
    ADD COLUMN `contactPhone` VARCHAR(255) NOT NULL,
    ADD COLUMN `email` VARCHAR(255) NOT NULL,
    MODIFY `citycode` JSON NOT NULL;

-- AlterTable
ALTER TABLE `User` DROP COLUMN `role`,
    ADD COLUMN `role_id` INTEGER NOT NULL,
    MODIFY `code` VARCHAR(255) NOT NULL DEFAULT '0',
    MODIFY `email` VARCHAR(255) NOT NULL DEFAULT '',
    MODIFY `contactTel` VARCHAR(255) NOT NULL DEFAULT '';

-- DropTable
DROP TABLE `Auth`;

-- DropTable
DROP TABLE `Customer`;

-- DropTable
DROP TABLE `InvoiceType`;

-- DropTable
DROP TABLE `Paper`;

-- DropTable
DROP TABLE `Product`;

-- DropTable
DROP TABLE `Purchase`;

-- DropTable
DROP TABLE `Saleorder`;

-- DropTable
DROP TABLE `customer_invoiceinfo`;

-- DropTable
DROP TABLE `customer_receivinginfo`;

-- DropTable
DROP TABLE `product_sku`;

-- DropTable
DROP TABLE `product_type`;

-- DropTable
DROP TABLE `purchase_list`;

-- DropTable
DROP TABLE `saleorder_invoice`;

-- DropTable
DROP TABLE `saleorder_list`;

-- DropTable
DROP TABLE `user_auth`;

-- DropTable
DROP TABLE `warehouse_stock`;

-- DropTable
DROP TABLE `warehouse_stock_in`;

-- DropTable
DROP TABLE `warehouse_stock_out`;

-- CreateTable
CREATE TABLE `Role` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(100) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `Permission` JSON NOT NULL,

    UNIQUE INDEX `Role_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Menu` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `icon` VARCHAR(191) NULL,
    `sort` INTEGER NOT NULL DEFAULT 999,
    `disabled` BOOLEAN NOT NULL DEFAULT false,
    `type` ENUM('item', 'group', 'sub') NOT NULL,
    `parentId` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RoleMenu` (
    `role_id` INTEGER NOT NULL,
    `menu_id` INTEGER NOT NULL,

    UNIQUE INDEX `RoleMenu_role_id_menu_id_key`(`role_id`, `menu_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Permission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(100) NOT NULL,
    `name` VARCHAR(255) NOT NULL,

    UNIQUE INDEX `Permission_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `System` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `installed` BOOLEAN NOT NULL DEFAULT false,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `menuCount` INTEGER NOT NULL DEFAULT 0,
    `permissionCount` INTEGER NOT NULL DEFAULT 0,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CommonDefect` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `code` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Materiel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `category` VARCHAR(255) NOT NULL,
    `attribute` VARCHAR(255) NOT NULL,
    `type` VARCHAR(255) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `code` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `model` VARCHAR(255) NOT NULL,
    `unit` VARCHAR(255) NOT NULL,
    `sepc` VARCHAR(255) NULL,
    `picpath` JSON NULL,
    `stock` DECIMAL NOT NULL DEFAULT 0,
    `onway_stock` DECIMAL NOT NULL DEFAULT 0,
    `lowstock` DECIMAL NOT NULL DEFAULT 0,
    `maxstock` DECIMAL NOT NULL DEFAULT 0,
    `createuser_id` INTEGER NOT NULL,
    `useable` BOOLEAN NOT NULL DEFAULT true,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Materiel_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BomItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `materiel_id` INTEGER NOT NULL,
    `quantity` DECIMAL NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProductionTask` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `materiel_id` INTEGER NOT NULL,
    `quantity` DECIMAL NOT NULL,
    `completed_quantity` DECIMAL NOT NULL DEFAULT 0,
    `qualified_quantity` DECIMAL NOT NULL DEFAULT 0,
    `unqualified_quantity` DECIMAL NOT NULL DEFAULT 0,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,
    `startAt` DATETIME(3) NOT NULL,
    `endAt` DATETIME(3) NOT NULL,
    `actualStartAt` DATETIME(3) NULL,
    `actualEndAt` DATETIME(3) NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `finishCondition` VARCHAR(50) NOT NULL DEFAULT 'quantity',

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProductionTaskUsers` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `task_id` INTEGER NOT NULL,
    `user_id` INTEGER NOT NULL,

    UNIQUE INDEX `ProductionTaskUsers_task_id_user_id_key`(`task_id`, `user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProductionPlan` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(255) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `startTime` DATETIME(3) NOT NULL,
    `endTime` DATETIME(3) NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `completion` DOUBLE NOT NULL DEFAULT 0,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProductionPlanItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `production_plan_id` INTEGER NOT NULL,
    `materiel_id` INTEGER NOT NULL,
    `quantity` DECIMAL NOT NULL,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PurchaseOrder` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `orderNo` VARCHAR(255) NOT NULL,
    `supplierId` INTEGER NOT NULL,
    `totalAmount` DECIMAL(10, 2) NOT NULL,
    `expectedDeliveryDate` DATETIME(3) NULL,
    `status` VARCHAR(50) NOT NULL DEFAULT 'draft',
    `note` TEXT NULL,
    `userId` INTEGER NOT NULL,
    `submittedAt` DATETIME(3) NULL,
    `approvedBy` INTEGER NULL,
    `approvedAt` DATETIME(3) NULL,
    `rejectReason` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `warehouseId` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PurchaseOrderItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `purchaseOrderId` INTEGER NOT NULL,
    `materialId` INTEGER NOT NULL,
    `quantity` DECIMAL(10, 2) NOT NULL,
    `unitPrice` DECIMAL(10, 2) NOT NULL,
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PurchaseReceiptRecord` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `purchaseOrderId` INTEGER NOT NULL,
    `userId` INTEGER NOT NULL,
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PurchaseReceiptItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `purchaseReceiptId` INTEGER NOT NULL,
    `orderItemId` INTEGER NOT NULL,
    `receivedQuantity` DECIMAL(10, 2) NOT NULL,
    `batchNo` VARCHAR(255) NOT NULL,
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Stock` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `materiel_id` INTEGER NOT NULL,
    `warehouse_id` INTEGER NOT NULL,
    `batch_no` VARCHAR(255) NOT NULL,
    `quantity` DECIMAL(10, 2) NOT NULL,
    `production_date` DATETIME(3) NULL,
    `expiry_date` DATETIME(3) NULL,
    `location` VARCHAR(255) NULL,
    `status` VARCHAR(50) NOT NULL DEFAULT 'normal',
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Stock_materiel_id_warehouse_id_batch_no_key`(`materiel_id`, `warehouse_id`, `batch_no`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `User` ADD CONSTRAINT `User_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `Role`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Menu` ADD CONSTRAINT `Menu_parentId_fkey` FOREIGN KEY (`parentId`) REFERENCES `Menu`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoleMenu` ADD CONSTRAINT `RoleMenu_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `Role`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RoleMenu` ADD CONSTRAINT `RoleMenu_menu_id_fkey` FOREIGN KEY (`menu_id`) REFERENCES `Menu`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Materiel` ADD CONSTRAINT `Materiel_createuser_id_fkey` FOREIGN KEY (`createuser_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BomItem` ADD CONSTRAINT `BomItem_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionTask` ADD CONSTRAINT `ProductionTask_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionTaskUsers` ADD CONSTRAINT `ProductionTaskUsers_task_id_fkey` FOREIGN KEY (`task_id`) REFERENCES `ProductionTask`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionTaskUsers` ADD CONSTRAINT `ProductionTaskUsers_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionPlanItem` ADD CONSTRAINT `ProductionPlanItem_production_plan_id_fkey` FOREIGN KEY (`production_plan_id`) REFERENCES `ProductionPlan`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionPlanItem` ADD CONSTRAINT `ProductionPlanItem_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseOrder` ADD CONSTRAINT `PurchaseOrder_supplierId_fkey` FOREIGN KEY (`supplierId`) REFERENCES `Supplier`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseOrder` ADD CONSTRAINT `PurchaseOrder_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseOrder` ADD CONSTRAINT `PurchaseOrder_warehouseId_fkey` FOREIGN KEY (`warehouseId`) REFERENCES `Warehouse`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseOrderItem` ADD CONSTRAINT `PurchaseOrderItem_purchaseOrderId_fkey` FOREIGN KEY (`purchaseOrderId`) REFERENCES `PurchaseOrder`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseOrderItem` ADD CONSTRAINT `PurchaseOrderItem_materialId_fkey` FOREIGN KEY (`materialId`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseReceiptRecord` ADD CONSTRAINT `PurchaseReceiptRecord_purchaseOrderId_fkey` FOREIGN KEY (`purchaseOrderId`) REFERENCES `PurchaseOrder`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseReceiptRecord` ADD CONSTRAINT `PurchaseReceiptRecord_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseReceiptItem` ADD CONSTRAINT `PurchaseReceiptItem_purchaseReceiptId_fkey` FOREIGN KEY (`purchaseReceiptId`) REFERENCES `PurchaseReceiptRecord`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PurchaseReceiptItem` ADD CONSTRAINT `PurchaseReceiptItem_orderItemId_fkey` FOREIGN KEY (`orderItemId`) REFERENCES `PurchaseOrderItem`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Stock` ADD CONSTRAINT `Stock_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Stock` ADD CONSTRAINT `Stock_warehouse_id_fkey` FOREIGN KEY (`warehouse_id`) REFERENCES `Warehouse`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
