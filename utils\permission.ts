import { ref } from "vue";

const userPermissions = ref<UserPermissions>({
  roles: [],
  permissions: [],
});

export const usePermission = () => {
  // 检查是否有某个权限
  const hasPermission = (permissionCode: string): boolean => {
    console.log(
      permissionCode,
      userPermissions.value.permissions.includes(permissionCode)
    );
    return userPermissions.value.permissions.includes(permissionCode);
  };

  // 检查是否有多个权限中的任意一个
  const hasAnyPermission = (permissionCodes: string[]): boolean => {
    return permissionCodes.some((code) => hasPermission(code));
  };

  // 检查是否拥有所有权限
  const hasAllPermissions = (permissionCodes: string[]): boolean => {
    return permissionCodes.every((code) => hasPermission(code));
  };

  // 设置用户权限
  const setPermissions = (permissions: UserPermissions) => {
    userPermissions.value = permissions;
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    setPermissions,
  };
};
