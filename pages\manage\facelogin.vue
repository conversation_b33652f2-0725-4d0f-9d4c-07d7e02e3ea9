<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from "vue";
  import { useRouter } from "#app";
  import * as faceapi from "face-api.js";
  import { message } from "ant-design-vue";

  const router = useRouter();
  const api = useApiTrpc();

  // 状态变量
  const videoRef = ref<HTMLVideoElement | null>(null);
  const canvasRef = ref<HTMLCanvasElement | null>(null);
  const isLoading = ref(false);
  const isCameraReady = ref(false);
  const stream = ref<MediaStream | null>(null);

  // 初始化face-api模型
  const initFaceApi = async () => {
    try {
      const faceApiUrl = "https://face.nngene.com/models";
      await faceapi.nets.tinyFaceDetector.loadFromUri(faceApiUrl);
      await faceapi.nets.faceLandmark68Net.loadFromUri(faceApiUrl);
      await faceapi.nets.faceRecognitionNet.loadFromUri(faceApiUrl);
      await faceapi.nets.ssdMobilenetv1.loadFromUri(faceApiUrl);
      await faceapi.nets.mtcnn.loadFromUri(faceApiUrl);
      // await faceapi.nets.ssdMobilenetv1.loadFromUri("/models");
    } catch (error) {
      message.error("人脸识别模型加载失败");
      console.error("Failed to load face-api models:", error);
    }
  };

  // 启动摄像头
  const startCamera = async () => {
    try {
      stream.value = await navigator.mediaDevices.getUserMedia({
        video: {
          width: 640,
          height: 480,
        },
      });

      if (videoRef.value) {
        videoRef.value.srcObject = stream.value;
        isCameraReady.value = true;
      }
    } catch (error) {
      message.error("无法访问摄像头");
      console.error("Failed to access camera:", error);
    }
  };

  // 停止摄像头
  const stopCamera = () => {
    if (stream.value) {
      stream.value.getTracks().forEach((track) => track.stop());
      stream.value = null;
      isCameraReady.value = false;
    }
  };

  // 检测人脸并登录
  const handleLogin = async () => {
    if (!videoRef.value || !canvasRef.value || isLoading.value) return;

    isLoading.value = true;
    try {
      const detections = await faceapi
        .detectSingleFace(videoRef.value, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detections) {
        message.warning("未检测到人脸，请确保面部在摄像头范围内");
        isLoading.value = false;
        return;
      }

      // 获取人脸图像的base64数据
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      if (!context) {
        throw new Error("无法创建canvas上下文");
      }

      // 设置canvas尺寸为视频的实际尺寸
      canvas.width = videoRef.value.videoWidth;
      canvas.height = videoRef.value.videoHeight;

      // 绘制当前视频帧
      context.drawImage(videoRef.value, 0, 0, canvas.width, canvas.height);

      // 获取人脸区域
      const box = detections.detection.box;
      const faceCanvas = document.createElement("canvas");
      const faceContext = faceCanvas.getContext("2d");
      if (!faceContext) {
        throw new Error("无法创建人脸canvas上下文");
      }

      // 设置人脸区域canvas的尺寸
      const padding = 50; // 人脸区域周围的额外padding
      faceCanvas.width = box.width + padding * 2;
      faceCanvas.height = box.height + padding * 2;

      // 从原始canvas中裁剪人脸区域（包含padding）
      faceContext.drawImage(
        canvas,
        box.x - padding,
        box.y - padding,
        box.width + padding * 2,
        box.height + padding * 2,
        0,
        0,
        faceCanvas.width,
        faceCanvas.height
      );

      // 获取人脸区域的base64数据
      const faceImageBase64 = faceCanvas.toDataURL("image/jpeg", 0.8);

      // 将人脸特征数据和base64图像发送到后端进行验证
      const response = await api.public.auth.faceLogin.mutate({
        faceDescriptor: Array.from(detections.descriptor),
        faceImage: faceImageBase64,
      });
      console.log(response);
      if (response.code === 1) {
        message.success("登录成功");
        stopCamera();
        router.push("/manage/mes");
      } else {
        message.error(response.data?.message || "人脸验证失败");
      }
    } catch (error) {
      message.error("登录过程中发生错误");
      console.error("Login error:", error);
    } finally {
      isLoading.value = false;
    }
  };

  // 生命周期钩子
  onMounted(async () => {
    await initFaceApi();
    await startCamera();
  });

  onUnmounted(() => {
    stopCamera();
  });
</script>

<template>
  <div class="face-login-container">
    <a-card class="face-login-card" title="人脸识别登录">
      <div class="video-container">
        <video
          ref="videoRef"
          autoplay
          muted
          playsinline
          :class="{ 'camera-ready': isCameraReady }"
        ></video>
        <canvas ref="canvasRef" class="face-canvas"></canvas>
      </div>

      <div class="controls">
        <a-button
          type="primary"
          :loading="isLoading"
          :disabled="!isCameraReady"
          @click="handleLogin"
        >
          {{ isLoading ? "验证中..." : "开始验证" }}
        </a-button>

        <a-button @click="() => router.push('/manage/login')">
          返回普通登录
        </a-button>
      </div>

      <div class="tips">
        <p>请确保:</p>
        <ul>
          <li>面部在摄像头范围内</li>
          <li>光线充足</li>
          <li>正面面对摄像头</li>
        </ul>
      </div>
    </a-card>
  </div>
</template>

<style scoped>
  .face-login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f0f2f5;
  }

  .face-login-card {
    width: 100%;
    max-width: 800px;
    margin: 20px;
  }

  .video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
  }

  video {
    width: 100%;
    height: auto;
    transform: scaleX(-1);
  }

  .face-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .controls {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
  }

  .tips {
    margin-top: 20px;
    padding: 16px;
    background-color: #f6f6f6;
    border-radius: 4px;
  }

  .tips ul {
    margin: 8px 0 0 20px;
    padding: 0;
  }

  .tips li {
    margin-bottom: 4px;
    color: #666;
  }

  .camera-ready {
    border: 2px solid #52c41a;
  }
</style>
