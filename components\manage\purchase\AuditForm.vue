<template>
  <div class="audit-form">
    <a-spin :spinning="loading">
      <template v-if="orderData">
        <!-- 订单基本信息 -->
        <div class="info-section">
          <h3>订单基本信息</h3>
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="订单编号">{{
              orderData.orderNo
            }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag :color="getStatusColor(orderData.status)">
                {{ getStatusText(orderData.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="收货仓库">
              {{ orderData.warehouse?.name || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="供应商">{{
              orderData.supplier?.name
            }}</a-descriptions-item>
            <a-descriptions-item label="总金额">{{
              formatCurrency(orderData.totalAmount)
            }}</a-descriptions-item>
            <a-descriptions-item label="预计到货日期">{{
              orderData.expectedDeliveryDate || "-"
            }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{
              formatDate(orderData.createdAt)
            }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 订单物料列表 -->
        <div class="info-section">
          <h3>物料清单</h3>
          <a-table
            :dataSource="orderData.items || []"
            :columns="materialsColumns"
            :pagination="false"
            rowKey="id"
            size="small"
            bordered
          >
            <!-- 自定义列渲染 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'materialName'">
                {{ record.material?.name }}
              </template>
              <template v-else-if="column.key === 'materialSpec'">
                {{ record.material?.spec || "-" }}
              </template>
              <template v-else-if="column.key === 'materialUnit'">
                {{ record.material?.unit || "-" }}
              </template>
              <template v-else-if="column.key === 'totalPrice'">
                {{ formatCurrency(record.quantity * record.unitPrice) }}
              </template>
            </template>
          </a-table>
        </div>

        <!-- 审核表单 -->
        <div class="form-section">
          <h3>审核意见</h3>
          <a-form :model="formData" layout="vertical">
            <a-form-item
              name="auditResult"
              label="审核结果"
              :rules="[{ required: true, message: '请选择审核结果' }]"
            >
              <a-radio-group v-model:value="formData.auditResult">
                <a-radio :value="true">通过</a-radio>
                <a-radio :value="false">拒绝</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item
              v-if="formData.auditResult === false"
              name="rejectReason"
              label="拒绝原因"
              :rules="[
                { required: true, message: '请填写拒绝原因' },
                { max: 200, message: '拒绝原因不能超过200个字符' },
              ]"
            >
              <a-textarea
                v-model:value="formData.rejectReason"
                placeholder="请输入拒绝原因"
                :rows="4"
                :maxlength="200"
                show-count
              />
            </a-form-item>

            <a-form-item name="comments" label="审核备注">
              <a-textarea
                v-model:value="formData.comments"
                placeholder="请输入审核备注（可选）"
                :rows="3"
                :maxlength="200"
                show-count
              />
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button
                  type="primary"
                  @click="handleSubmit"
                  :loading="submitting"
                >
                  提交审核
                </a-button>
                <a-button @click="handleCancel">取消</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </template>

      <!-- 无数据时显示 -->
      <a-empty v-else description="未找到订单数据" />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 定义props
  const props = defineProps({
    orderId: {
      type: Number,
      required: true,
    },
  });

  // 定义事件
  const emit = defineEmits(["success", "cancel"]);

  // 订单状态定义
  const orderStatus = [
    { label: "草稿", value: "draft" },
    { label: "待审核", value: "pending" },
    { label: "已审核", value: "approved" },
    { label: "已拒绝", value: "rejected" },
    { label: "部分收货", value: "partially_received" },
    { label: "完成", value: "completed" },
    { label: "取消", value: "cancelled" },
  ];

  // 状态显示辅助函数
  const getStatusText = (status: string) => {
    const item = orderStatus.find((item) => item.value === status);
    return item ? item.label : "未知状态";
  };

  const getStatusColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
      draft: "default",
      pending: "processing",
      approved: "success",
      rejected: "error",
      partially_received: "warning",
      completed: "success",
      cancelled: "default",
    };
    return statusColorMap[status] || "default";
  };

  // 格式化函数
  const formatDate = (dateStr: string) => {
    if (!dateStr) return "-";
    return dayjs(dateStr).format("YYYY-MM-DD HH:mm:ss");
  };

  const formatCurrency = (amount: any) => {
    if (amount === null || amount === undefined) return "¥ 0.00";
    const numAmount = Number(amount);
    return !isNaN(numAmount) ? `¥ ${numAmount.toFixed(2)}` : "¥ 0.00";
  };

  // 数据状态
  const loading = ref(false);
  const submitting = ref(false);
  const orderData = ref<any>(null);

  // 表单数据
  const formData = reactive({
    auditResult: true, // 默认通过
    rejectReason: "",
    comments: "",
  });

  // 表格列定义
  const materialsColumns = [
    { title: "物料名称", dataIndex: "materialName", key: "materialName" },
    { title: "规格", dataIndex: "materialSpec", key: "materialSpec" },
    { title: "单位", dataIndex: "materialUnit", key: "materialUnit" },
    { title: "数量", dataIndex: "quantity", key: "quantity" },
    { title: "单价", dataIndex: "unitPrice", key: "unitPrice" },
    { title: "总价", dataIndex: "totalPrice", key: "totalPrice" },
    { title: "备注", dataIndex: "note", key: "note" },
  ];

  // 获取订单详情
  const fetchOrderDetail = async () => {
    loading.value = true;
    try {
      const result = await useApiTrpc().admin.purchase.getPurchaseOrder.query({
        id: props.orderId,
      });

      if (result.code === 200) {
        orderData.value = result.data;

        // 检查订单状态是否为待审核
        if (orderData.value.status !== "pending") {
          message.warning("当前订单状态不是待审核状态");
        }
      } else {
        message.error(result.message || "获取订单详情失败");
      }
    } catch (error) {
      console.error("获取订单详情失败", error);
      message.error("获取订单详情失败，请稍后再试");
    } finally {
      loading.value = false;
    }
  };

  // 提交审核
  const handleSubmit = async () => {
    // 基本验证
    if (formData.auditResult === false && !formData.rejectReason.trim()) {
      message.error("请填写拒绝原因");
      return;
    }

    submitting.value = true;
    try {
      // 准备请求参数
      const params = {
        id: props.orderId,
        approved: formData.auditResult,
        rejectReason: formData.auditResult
          ? undefined
          : formData.rejectReason.trim(),
        comments: formData.comments.trim() || undefined,
      };

      // 调用审核API
      const result =
        await useApiTrpc().admin.purchase.approvePurchaseOrder.mutate(params);

      if (result.code === 200) {
        message.success(formData.auditResult ? "订单审核通过" : "订单已被拒绝");
        emit("success", {
          approved: formData.auditResult,
          comments: formData.comments,
        });
      } else {
        message.error(result.message || "审核提交失败");
      }
    } catch (error) {
      console.error("审核提交失败", error);
      message.error("审核提交失败，请稍后再试");
    } finally {
      submitting.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    emit("cancel");
  };

  // 初始化
  onMounted(() => {
    fetchOrderDetail();
  });
</script>

<style scoped>
  .audit-form {
    padding: 0 10px;
  }

  .info-section,
  .form-section {
    margin-bottom: 24px;
  }

  .info-section h3,
  .form-section h3 {
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 16px;
  }
</style>
