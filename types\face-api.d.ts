declare module "face-api.js" {
  export const nets: {
    tinyFaceDetector: {
      loadFromUri: (path: string) => Promise<void>;
    };
    faceLandmark68Net: {
      loadFromUri: (path: string) => Promise<void>;
    };
    faceRecognitionNet: {
      loadFromUri: (path: string) => Promise<void>;
    };
    ssdMobilenetv1: {
      loadFromUri: (path: string) => Promise<void>;
    };
    mtcnn: {
      loadFromUri: (path: string) => Promise<void>;
    };
  };

  export interface ITinyFaceDetectorOptions {
    inputSize?: number;
    scoreThreshold?: number;
  }

  export class TinyFaceDetectorOptions {
    constructor(options?: ITinyFaceDetectorOptions);
  }

  export function detectSingleFace(
    input: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement,
    options: TinyFaceDetectorOptions
  ): DetectSingleFaceReturn;

  export type DetectSingleFaceReturn = {
    withFaceLandmarks(): WithFaceLandmarksReturn;
  };

  export type WithFaceLandmarksReturn = {
    withFaceDescriptor(): Promise<WithFaceDescriptor>;
  };

  export class WithFaceDescriptor {
    detection: {
      box: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
    };
    landmarks: {
      positions: Array<{ x: number; y: number }>;
    };
    descriptor: Float32Array;
  }

  export function matchDimensions(
    canvas: HTMLCanvasElement,
    dimensions: { width: number; height: number }
  ): void;

  export function resizeResults(
    results: WithFaceDescriptor,
    dimensions: { width: number; height: number }
  ): WithFaceDescriptor;

  export const draw: {
    drawDetections(
      canvas: HTMLCanvasElement,
      results: WithFaceDescriptor | WithFaceDescriptor[]
    ): void;
    drawFaceLandmarks(
      canvas: HTMLCanvasElement,
      results: WithFaceDescriptor | WithFaceDescriptor[]
    ): void;
  };
}
