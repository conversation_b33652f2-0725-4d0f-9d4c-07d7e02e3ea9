<template>
  <a-card title="产量统计">
    <!-- 搜索表单 -->
    <a-form layout="inline" :model="searchForm">
      <a-form-item label="时间范围">
        <a-range-picker
          v-model:value="searchForm.dateRange"
          :format="dateFormat"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          @change="handleSearch"
        />
      </a-form-item>
      <a-form-item label="产品">
        <manage-materiel-modelselector
          v-model:value="searchForm.materielId"
          :multiple="false"
          placeholder="请选择产品"
          @change="handleSearch"
        />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="resetSearch">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <!-- 统计卡片 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="6">
        <a-card>
          <statistic
            title="总计划数量"
            :value="statistics.totalPlanQuantity"
            :precision="0"
            style="margin-right: 16px"
          >
            <template #suffix>
              <span>件</span>
            </template>
          </statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="已完成数量"
            :value="statistics.totalCompletedQuantity"
            :precision="0"
            style="margin-right: 16px"
          >
            <template #suffix>
              <span>件</span>
            </template>
          </statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="合格品数量"
            :value="statistics.totalQualifiedQuantity"
            :precision="0"
            :value-style="{ color: '#3f8600' }"
          >
            <template #suffix>
              <span>件</span>
            </template>
          </statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="不合格品数量"
            :value="statistics.totalUnqualifiedQuantity"
            :precision="0"
            :value-style="{ color: '#cf1322' }"
          >
            <template #suffix>
              <span>件</span>
            </template>
          </statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表展示 -->
    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="产量趋势">
          <div ref="productionTrendChart" style="height: 300px"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="合格率分析">
          <div ref="qualityRateChart" style="height: 300px"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 数据表格 -->
    <div style="margin-top: 16px">
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :query="queryFn"
        :scroll="{ x: 1300 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'completion'">
            <a-progress
              :percent="getCompletionRate(record)"
              :status="getCompletionStatus(record)"
            />
          </template>
          <template v-if="column.key === 'qualifiedRate'">
            <a-progress
              :percent="getQualifiedRate(record)"
              :status="getQualifiedStatus(record)"
            />
          </template>
        </template>
      </manage-base-table>
    </div>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from "vue";
  import { Statistic } from "ant-design-vue";
  import dayjs from "dayjs";
  import * as echarts from "echarts";
  import type { TableColumnProps } from "ant-design-vue";

  // 日期格式
  const dateFormat = "YYYY-MM-DD";

  // 搜索表单数据
  const searchForm = reactive({
    dateRange: [dayjs().startOf("month"), dayjs()] as [
      dayjs.Dayjs,
      dayjs.Dayjs
    ],
    materielId: undefined as number | undefined,
  });

  // 统计数据
  const statistics = reactive({
    totalPlanQuantity: 0,
    totalCompletedQuantity: 0,
    totalQualifiedQuantity: 0,
    totalUnqualifiedQuantity: 0,
  });

  // 表格列定义
  const columns: TableColumnProps[] = [
    {
      title: "工单编号",
      dataIndex: "code",
      width: 160,
      fixed: "left",
    },
    {
      title: "产品名称",
      dataIndex: "productName",
      width: 160,
      customRender: ({ record }) => record.Materiel?.name || "-",
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
      width: 120,
    },
    {
      title: "完成数量",
      dataIndex: "completed_quantity",
      width: 120,
    },
    {
      title: "合格品数量",
      dataIndex: "qualified_quantity",
      width: 120,
    },
    {
      title: "不合格品数量",
      dataIndex: "unqualified_quantity",
      width: 120,
    },
    {
      title: "完成率",
      key: "completion",
      width: 160,
    },
    {
      title: "合格率",
      key: "qualifiedRate",
      width: 160,
    },
    {
      title: "开始时间",
      dataIndex: "startAt",
      width: 120,
      customRender: ({ text }) =>
        text ? dayjs(text).format("YYYY-MM-DD") : "-",
    },
    {
      title: "结束时间",
      dataIndex: "endAt",
      width: 120,
      customRender: ({ text }) =>
        text ? dayjs(text).format("YYYY-MM-DD") : "-",
    },
  ];

  // 图表实例
  const productionTrendChart = ref<HTMLDivElement>();
  const qualityRateChart = ref<HTMLDivElement>();
  let trendChartInstance: echarts.ECharts | null = null;
  let qualityChartInstance: echarts.ECharts | null = null;

  // 查询函数
  const queryFn = async (params: any) => {
    try {
      const [startDate, endDate] = searchForm.dateRange;
      const queryParams = {
        ...params,
        startDate: startDate?.format("YYYY-MM-DD"),
        endDate: endDate?.format("YYYY-MM-DD"),
        materielId: searchForm.materielId,
      };

      const result =
        await useApiTrpc().admin.production.queryProductionTask.query(
          queryParams
        );

      // 更新统计数据
      if (result.data?.result) {
        updateStatistics(result.data.result);
        updateCharts(result.data.result);
      }

      return result;
    } catch (error) {
      console.error("查询失败:", error);
      return { data: { result: [], total: 0 } };
    }
  };

  // 更新统计数据
  const updateStatistics = (data: any[]) => {
    statistics.totalPlanQuantity = data.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0
    );
    statistics.totalCompletedQuantity = data.reduce(
      (sum, item) => sum + Number(item.completed_quantity || 0),
      0
    );
    statistics.totalQualifiedQuantity = data.reduce(
      (sum, item) => sum + Number(item.qualified_quantity || 0),
      0
    );
    statistics.totalUnqualifiedQuantity = data.reduce(
      (sum, item) => sum + Number(item.unqualified_quantity || 0),
      0
    );
  };

  // 更新图表
  const updateCharts = (data: any[]) => {
    updateTrendChart(data);
    updateQualityChart(data);
  };

  // 更新产量趋势图表
  const updateTrendChart = (data: any[]) => {
    if (!productionTrendChart.value || !trendChartInstance) return;

    // 按日期分组数据
    const dateMap = new Map();
    data.forEach((item) => {
      const date = dayjs(item.startAt).format("YYYY-MM-DD");
      const current = dateMap.get(date) || { plan: 0, completed: 0 };
      dateMap.set(date, {
        plan: current.plan + Number(item.quantity || 0),
        completed: current.completed + Number(item.completed_quantity || 0),
      });
    });

    // 准备图表数据
    const dates = Array.from(dateMap.keys()).sort();
    const planData = dates.map((date) => dateMap.get(date).plan);
    const completedData = dates.map((date) => dateMap.get(date).completed);

    // 设置图表配置
    const option = {
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["计划数量", "完成数量"],
      },
      xAxis: {
        type: "category",
        data: dates,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "计划数量",
          type: "line",
          data: planData,
        },
        {
          name: "完成数量",
          type: "line",
          data: completedData,
        },
      ],
    };

    trendChartInstance.setOption(option);
  };

  // 更新质量分析图表
  const updateQualityChart = (data: any[]) => {
    if (!qualityRateChart.value || !qualityChartInstance) return;

    const totalCompleted = data.reduce(
      (sum, item) => sum + Number(item.completed_quantity || 0),
      0
    );
    const qualified = data.reduce(
      (sum, item) => sum + Number(item.qualified_quantity || 0),
      0
    );
    const unqualified = data.reduce(
      (sum, item) => sum + Number(item.unqualified_quantity || 0),
      0
    );

    const option = {
      tooltip: {
        trigger: "item",
        formatter: "{b}: {c} ({d}%)",
      },
      legend: {
        orient: "vertical",
        left: "left",
        data: ["合格品", "不合格品"],
      },
      series: [
        {
          type: "pie",
          radius: ["50%", "70%"],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: "inside",
            formatter: "{d}%",
          },
          data: [
            { value: qualified, name: "合格品" },
            { value: unqualified, name: "不合格品" },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };

    qualityChartInstance.setOption(option);
  };

  // 计算完成率
  const getCompletionRate = (record: any) => {
    if (!record.quantity) return 0;
    const rate =
      (Number(record.completed_quantity || 0) / Number(record.quantity)) * 100;
    return Math.min(Math.round(rate), 100);
  };

  // 获取完成状态
  const getCompletionStatus = (record: any) => {
    const rate = getCompletionRate(record);
    if (rate >= 100) return "success";
    if (rate >= 80) return "active";
    if (rate >= 50) return "normal";
    return "exception";
  };

  // 计算合格率
  const getQualifiedRate = (record: any) => {
    if (!record.completed_quantity) return 0;
    const rate =
      (Number(record.qualified_quantity || 0) /
        Number(record.completed_quantity)) *
      100;
    return Math.round(rate);
  };

  // 获取合格率状态
  const getQualifiedStatus = (record: any) => {
    const rate = getQualifiedRate(record);
    if (rate >= 98) return "success";
    if (rate >= 90) return "normal";
    return "exception";
  };

  // 禁用的日期
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf("day");
  };
const tableRef = ref();
  // 搜索
  const handleSearch = () => {
    tableRef.value?.query();
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.dateRange = [dayjs().startOf("month"), dayjs()];
    searchForm.materielId = undefined;
    handleSearch();
  };

  // 初始化图表
  onMounted(() => {
    if (productionTrendChart.value) {
      trendChartInstance = echarts.init(productionTrendChart.value);
    }
    if (qualityRateChart.value) {
      qualityChartInstance = echarts.init(qualityRateChart.value);
    }
    handleSearch();
  });

  // 组件卸载时销毁图表实例
  onUnmounted(() => {
    if (trendChartInstance) {
      trendChartInstance.dispose();
    }
    if (qualityChartInstance) {
      qualityChartInstance.dispose();
    }
  });
</script>
