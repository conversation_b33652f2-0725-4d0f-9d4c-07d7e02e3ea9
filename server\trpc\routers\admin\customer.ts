import {
  router,
  mergeRouters,
  tableResultProcedure,
  singleResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma, Prisma } from "@/lib/prisma";
import {
  customerQuerySchema,
  customerCreateSchema,
  customerUpdateSchema,
  customerReceivingInfoCreateSchema,
  customerReceivingInfoUpdateSchema,
  customerInvoiceInfoCreateSchema,
  customerInvoiceInfoUpdateSchema,
} from "~/schemas/customer";

// 查询客户列表
export const queryCustomer = tableResultProcedure
  .meta({ permission: ["customer:query"], authRequired: true })
  .input(customerQuerySchema)
  .query(async ({ input }) => {
    const { id, name, comname, email, tel, mobile, address, lock } = input;
    const where: Prisma.CustomerWhereInput = {};
    
    if (id) where.id = id;
    if (name) where.name = { contains: name };
    if (comname) where.comname = { contains: comname };
    if (email) where.email = { contains: email };
    if (tel) where.tel = { contains: tel };
    if (mobile) where.mobile = { contains: mobile };
    if (address) where.address = { contains: address };
    if (lock !== undefined) where.lock = lock;
    
    const customers = await prisma.customer.findManyWithCount({
      take: input.take,
      skip: input.skip,
      where: where,
      include: {
        Admin: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            ReceivingInfo: true,
            InvoiceInfo: true,
          },
        },
      },
    });
    
    return { code: 1, message: "success", data: customers };
  });

// 获取客户详情
export const getCustomer = singleResultProcedure
  .meta({ permission: ["customer:query"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .query(async ({ input }) => {
    const customer = await prisma.customer.findUnique({
      where: { id: input.id },
      include: {
        Admin: {
          select: {
            name: true,
          },
        },
        ReceivingInfo: true,
        InvoiceInfo: true,
      },
    });
    
    if (!customer) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "客户不存在",
      });
    }
    
    return { code: 1, message: "success", data: customer };
  });

// 添加客户
export const addCustomer = singleResultProcedure
  .meta({ permission: ["customer:add"], authRequired: true })
  .input(customerCreateSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const customer = await prisma.customer.create({
        data: {
          ...input,
          user_id: ctx.user.id,
        },
      });
      
      return { code: 1, message: "添加客户成功", data: customer };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "添加客户失败",
        cause: error,
      });
    }
  });

// 更新客户
export const updateCustomer = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(customerUpdateSchema)
  .mutation(async ({ input }) => {
    try {
      const { id, ...data } = input;
      
      const customer = await prisma.customer.update({
        where: { id },
        data,
      });
      
      return { code: 1, message: "更新客户成功", data: customer };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新客户失败",
        cause: error,
      });
    }
  });

// 锁定/解锁客户
export const toggleCustomerLock = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(z.object({ id: z.number(), lock: z.boolean() }))
  .mutation(async ({ input }) => {
    try {
      const customer = await prisma.customer.update({
        where: { id: input.id },
        data: { lock: input.lock },
      });
      
      return {
        code: 1,
        message: input.lock ? "客户已锁定" : "客户已解锁",
        data: customer,
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "操作失败",
        cause: error,
      });
    }
  });

// 删除客户
export const deleteCustomer = singleResultProcedure
  .meta({ permission: ["customer:delete"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input }) => {
    try {
      // 检查客户是否存在
      const customer = await prisma.customer.findUnique({
        where: { id: input.id },
        include: {
          ReceivingInfo: true,
          InvoiceInfo: true,
        },
      });
      
      if (!customer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "客户不存在",
        });
      }
      
      // 删除客户相关的收货信息和发票信息
      await prisma.$transaction([
        prisma.customerReceivingInfo.deleteMany({
          where: { customer_id: input.id },
        }),
        prisma.customerInvoiceInfo.deleteMany({
          where: { customer_id: input.id },
        }),
        prisma.customer.delete({
          where: { id: input.id },
        }),
      ]);
      
      return { code: 1, message: "删除客户成功" };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除客户失败",
        cause: error,
      });
    }
  });

// 添加客户收货信息
export const addCustomerReceivingInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(customerReceivingInfoCreateSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const receivingInfo = await prisma.customerReceivingInfo.create({
        data: {
          ...input,
          user_id: ctx.user.id,
        },
      });
      
      return { code: 1, message: "添加收货信息成功", data: receivingInfo };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "添加收货信息失败",
        cause: error,
      });
    }
  });

// 更新客户收货信息
export const updateCustomerReceivingInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(customerReceivingInfoUpdateSchema)
  .mutation(async ({ input }) => {
    try {
      const { id, ...data } = input;
      
      const receivingInfo = await prisma.customerReceivingInfo.update({
        where: { id },
        data,
      });
      
      return { code: 1, message: "更新收货信息成功", data: receivingInfo };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新收货信息失败",
        cause: error,
      });
    }
  });

// 删除客户收货信息
export const deleteCustomerReceivingInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input }) => {
    try {
      await prisma.customerReceivingInfo.delete({
        where: { id: input.id },
      });
      
      return { code: 1, message: "删除收货信息成功" };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除收货信息失败",
        cause: error,
      });
    }
  });

// 添加客户发票信息
export const addCustomerInvoiceInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(customerInvoiceInfoCreateSchema)
  .mutation(async ({ input }) => {
    try {
      const invoiceInfo = await prisma.customerInvoiceInfo.create({
        data: input,
      });
      
      return { code: 1, message: "添加发票信息成功", data: invoiceInfo };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "添加发票信息失败",
        cause: error,
      });
    }
  });

// 更新客户发票信息
export const updateCustomerInvoiceInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(customerInvoiceInfoUpdateSchema)
  .mutation(async ({ input }) => {
    try {
      const { id, ...data } = input;
      
      const invoiceInfo = await prisma.customerInvoiceInfo.update({
        where: { id },
        data,
      });
      
      return { code: 1, message: "更新发票信息成功", data: invoiceInfo };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新发票信息失败",
        cause: error,
      });
    }
  });

// 删除客户发票信息
export const deleteCustomerInvoiceInfo = singleResultProcedure
  .meta({ permission: ["customer:update"], authRequired: true })
  .input(z.object({ id: z.number() }))
  .mutation(async ({ input }) => {
    try {
      await prisma.customerInvoiceInfo.delete({
        where: { id: input.id },
      });
      
      return { code: 1, message: "删除发票信息成功" };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除发票信息失败",
        cause: error,
      });
    }
  });

// 合并所有客户相关路由
export const customerRouter = router({
  queryCustomer,
  getCustomer,
  addCustomer,
  updateCustomer,
  toggleCustomerLock,
  deleteCustomer,
  addCustomerReceivingInfo,
  updateCustomerReceivingInfo,
  deleteCustomerReceivingInfo,
  addCustomerInvoiceInfo,
  updateCustomerInvoiceInfo,
  deleteCustomerInvoiceInfo,
});
