<template>
  <a-modal
    v-model:open="visible"
    width="800px"
    title="欢迎使用,请登录"
    centered
  >
    <a-row style="margin-top: 15px">
      <a-col :span="10" style="display: flex; text-align: center">
        <div :style="loginPicStyle"></div>
      </a-col>
      <a-col :span="14">
        <a-form
          ref="formRef"
          :model="form"
          :rules="formRule"
          layout="vertical"
          @submit="login"
          v-on:keyup.enter="login"
        >
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="form.username" v-on:keyup:enter="login">
              <template #prefix>
                <UserOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="密码" name="password">
            <a-input-password
              v-model:value="form.password"
              autocomplete="off"
              v-on:keyup:enter="login"
            >
              <template #prefix>
                <LockOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item label="验证码" name="verifycode">
            <a-input
              v-model:value="form.verifycode"
              autocomplete="off"
              v-on:keyup:enter="login"
            >
              <template #prefix>
                <CheckCircleOutlined class="site-form-item-icon" />
              </template>
              <template #addonAfter>
                <img
                  :src="captcha"
                  :preview="false"
                  style="height: 28px"
                  @click="refreshCaptcha()"
                />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item>
            <a-button :loading="status" type="primary" @click="login" block
              >登录</a-button
            >
          </a-form-item>
        </a-form>
      </a-col>
    </a-row>
    <template #footer>
      <!-- <a-button @click="handleCancel">取消</a-button> -->
      <!-- <a-button :loading="status" type="primary" @click="login" block
        >登录</a-button
      > -->
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
  import type { Rule } from "ant-design-vue/es/form";
  import {
    UserOutlined,
    LockOutlined,
    CheckCircleOutlined,
  } from "@ant-design/icons-vue";
  import type { CSSProperties } from "vue";
  const { $emitter } = useNuxtApp();
  const visible = defineModel<boolean>({ required: true });
  const form = reactive({
    username: "",
    password: "",
    verifycode: "",
    role: "",
  });
  const emits = defineEmits<{
    login: [typeof form];
    refreshCaptcha: [void];
  }>();
  const status = defineModel<boolean>("status", { required: false });
  const formLabelWidth = 100;
  const formRef = ref();

  const formRule: Record<string, Rule[]> = {
    username: [{ required: true, message: "请填写账号", trigger: "blur" }],
    password: [{ required: true, message: "请填写密码", trigger: "blur" }],
    verifycode: [{ required: true, message: "请填写验证码", trigger: "blur" }],
  };
  // const props = defineProps({
  //   captcha: {
  //     type: Function as PropType<() => Promise<string>>,
  //     required: true,
  //   },
  // });
  const trpc = useApiTrpc();
  // const { data, refresh: refreshCaptcha } =
  //   await trpc.public.auth.captcha.useQuery({});
  // const captcha = computed(() => {
  //   if (data.value) {
  //     return data.value.data;
  //   }
  //   return "";
  // });
  const captcha = ref("");
  const refreshCaptcha = async () => {
    const result = await trpc.public.auth.captcha.query({});
    if (result.data) {
      captcha.value = result.data;
    }
  };
  watch(
    visible,
    () => {
      if (visible.value) {
        refreshCaptcha();
      }
    },
    { immediate: true }
  );
  const loginPicStyle: CSSProperties = {
    padding: "5px",
    width: "90%",
    height: "90%",
    backgroundImage: `url('/img/ae47bb32-1857-48c1-8901-ff4e25d02cf9.jpg')`,
    backgroundSize: "cover",
  };

  const handleCancel = () => {
    visible.value = false;
    status.value = false;
  };
  const login = async () => {
    status.value = true;
    formRef.value
      .validate()
      .then(async (callback: any) => {
        // emits("login", form);
        dologin(form);
      })
      .catch((error: any) => {
        status.value = false;
        console.log("error", error);
        message.error("登录失败");
      });
  };
  const dologin = async (form: any) => {
    // const result = await useAuth().login(form);
    const result = await trpc.public.auth.login.mutate({
      username: form.username,
      password: form.password,
      verifycode: form.verifycode,
    });
    if (!result.code) {
      $emitter.emit("loginFailed", result.message);
    } else {
      if (result.data) {
        setTimeout(() => {
          visible.value = false;
          status.value = false;
          $emitter.emit("loginSuccessed", result.data);
        }, 500);
      }
    }
  };
</script>
