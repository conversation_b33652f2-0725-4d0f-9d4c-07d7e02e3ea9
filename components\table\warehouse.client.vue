<template>
  <UiStoremanageTable
    ref="tableRef"
    :columns="table.columns"
    :query="useApiFetch.queryWarehouseList"
    :model="table.model"
  >
    <template #searchBox>
      <a-form-item name="name" label="仓库名称">
        <a-input v-model:value="table.model.name"></a-input>
      </a-form-item>
    </template>
  </UiStoremanageTable>
</template>
<script lang="ts" setup>
  import FormUserEdit from "../form/user/edit.client.vue";
  import { Tag, Divider } from "ant-design-vue";
  import { h } from "vue";
  const tableRef = ref();
  const table = useTable<{
    name: string;
    lock: boolean;
  }>({
    columns: [
      {
        title: "名称",
        dataIndex: "name",
      },
      {
        title: "地址",
        dataIndex: "address",
      },
      {
        title: "锁定",
        dataIndex: "lock",
      },
      {
        title: "Action",
        key: "action",
        customRender: (opt) => {
          // const deleteButton = h("a", "删除");
          const editButton = h(FormUserEdit, {
            model: opt.record,
            onSaveSuccess: () => {
              tableRef.value.query();
            },
          });
          const lockButton = opt.record.lock ? h("a", "解锁") : h("a", "锁定");
          const divider = h(Divider, { type: "vertical" });
          return h("span", [
            editButton,
            // divider,
            // lockButton
          ]);
        },
      },
    ],
  });
  const query = () => {
    tableRef.value.query();
  };
  onMounted(() => {
    // tableRef.value.query();
    nextTick(() => {
      // tableRef.value.query();
      // console.log(tableRef.value);
    });
  });
  defineExpose({
    reload: query,
  });
</script>
