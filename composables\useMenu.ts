import type { ItemType } from "ant-design-vue";
import type { VueElement } from "vue";

export function useMenu() {
  let items: Ref<ItemType[]> = ref([]);
  function addItem(
    label: VueElement | string,
    key: string,
    icon?: any,
    children?: ItemType[],
    type?: "group"
  ) {
    items.value.push({ key, icon, children, label, type } as ItemType);
  }
  function createItem(
    label: VueElement | string,
    key: string,
    icon?: any,
    children?: ItemType[],
    type?: "group"
  ): ItemType {
    return {
      key,
      icon,
      children,
      label,
      type,
    } as ItemType;
  }
  return { items, addItem, createItem };
}
