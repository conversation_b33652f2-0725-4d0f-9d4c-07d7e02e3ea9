<template>
  <NuxtLayout>
    <template #header>欢迎使用NNGENE管理系统</template>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon235499s.jpg" />
      </template>
      <a-card-meta title="账号密码登录"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showFaceLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon239056s.jpg" />
      </template>
      <a-card-meta title="人脸识别登录"></a-card-meta>
    </a-card>
    <!-- <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon596601s.jpg" />
      </template>
      <a-card-meta title="销售管理"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon232431s.jpg" />
      </template>
      <a-card-meta title="财务管理(新版)"></a-card-meta>
    </a-card>
    <a-card hoverable style="width: 200px" @click="showLogin">
      <template #cover>
        <img alt="example" src="/img/illusticon281598s.jpg" />
      </template>
      <a-card-meta title="供货单位"></a-card-meta>
    </a-card> -->
  </NuxtLayout>
</template>
<script setup lang="ts">
  definePageMeta({
    alias: "login",
    layout: "manage-login",
  });
  const { $emitter } = useNuxtApp();

  const showLogin = () => {
    autoFullscreen();
    $emitter.emit("changeLoginBoxStatus", true);
  };
  const showFaceLogin = () => {
    autoFullscreen();
    navigateTo("/manage/facelogin");
  };

  const goHome = async () => {
    const user = await useAuth().getUser();
    if (user) {
      navigateTo("/manage/mes");
    }
  };
  const autoFullscreen = () => {
    const route = useRoute();
    if (import.meta.client && /iPad|Android/.test(navigator.userAgent)) {
      console.log("检测到iPad/Android设备,自动全屏");
      import("~/utils/fullScreen").then(() => {
        useFullScreen()?.autoFullscreen();
      });
    }
  };
  $emitter.on("loginSuccessed", (user: API.LoginReturnType["data"]) => {
    goHome();
  });
</script>
