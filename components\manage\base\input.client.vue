<template>
  <a-input
    v-model:value="displayValue"
    v-bind="$attrs"
    @blur="handleBlur"
    @input="handleInput"
  />
</template>

<script setup lang="ts">
  import { computed, ref, watch } from "vue";
  import Decimal from "decimal.js";

  interface Props {
    // 最大值
    max?: number | string;
    // 最小值
    min?: number | string;
    // 小数位数
    precision?: number;
    // 是否允许负数
    allowNegative?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    max: Number.MAX_SAFE_INTEGER,
    min: Number.MIN_SAFE_INTEGER,
    precision: 2,
    allowNegative: true,
  });

  const value = defineModel<string | undefined>("value", {
    required: false,
    default: undefined,
  });

  // 显示值
  const displayValue = ref<string>("");

  // 格式化数字
  const formatNumber = (val: string | number | undefined): string => {
    if (val === undefined || val === "") return "";

    try {
      // 转换为字符串
      const strVal = String(val);

      // 移除非数字字符（保留小数点和负号）
      let formatted = strVal.replace(/[^\d.-]/g, "");

      // 处理负号
      if (!props.allowNegative) {
        formatted = formatted.replace(/-/g, "");
      } else {
        // 确保负号只出现在开头
        formatted = formatted.replace(/-/g, "");
        if (strVal.startsWith("-")) {
          formatted = "-" + formatted;
        }
      }

      // 处理小数点
      const parts = formatted.split(".");
      if (parts.length > 2) {
        // 保留第一个小数点
        formatted = parts[0] + "." + parts.slice(1).join("");
      }

      // 如果是空字符串或只有负号，直接返回
      if (formatted === "" || formatted === "-") return formatted;

      // 使用Decimal处理数值
      const decimalValue = new Decimal(formatted);

      // 处理范围限制
      const max = new Decimal(props.max);
      const min = new Decimal(props.min);

      if (decimalValue.greaterThan(max)) return max.toFixed(props.precision);
      if (decimalValue.lessThan(min)) return min.toFixed(props.precision);

      // 处理小数位数
      if (parts.length === 2) {
        if (parts[1].length > props.precision) {
          return decimalValue.toFixed(props.precision);
        }
        return formatted;
      }

      return formatted;
    } catch (error) {
      return "";
    }
  };

  // 解析数字
  const parseNumber = (val: string): string | undefined => {
    if (!val) return undefined;

    try {
      const decimalValue = new Decimal(val);

      // 处理范围限制
      const max = new Decimal(props.max);
      const min = new Decimal(props.min);

      if (decimalValue.greaterThan(max)) return max.toFixed(props.precision);
      if (decimalValue.lessThan(min)) return min.toFixed(props.precision);

      // 处理精度
      return decimalValue.toFixed(props.precision);
    } catch (error) {
      return undefined;
    }
  };

  // 监听value变化
  watch(
    () => value.value,
    (newVal) => {
      displayValue.value = formatNumber(newVal);
    },
    { immediate: true }
  );

  // 处理输入
  const handleInput = (e: Event) => {
    const target = e.target as HTMLInputElement;
    displayValue.value = formatNumber(target.value);
  };

  // 处理失焦
  const handleBlur = () => {
    const parsedValue = parseNumber(displayValue.value);
    value.value = parsedValue;
    displayValue.value = formatNumber(parsedValue);
  };
</script>
