<template>
  <a-modal
    v-model:open="visible"
    title="选择"
    @ok="handleOk"
    @cancel="handleCancel"
    width="80%"
  >
    <a-form layout="inline">
      <a-form-item label="物料名称">
        <manage-base-search-input
          v-model:value="searchForm.name"
          placeholder="请输入物料名称"
        />
      </a-form-item>
      <a-form-item label="物料编码">
        <manage-base-search-input
          v-model:value="searchForm.code"
          placeholder="请输入物料编码"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">搜索</a-button>
      </a-form-item>
    </a-form>
    <a-divider orientation="left">请选择</a-divider>
    <manage-base-table
      ref="tableRef"
      :columns="columns"
      :query="query"
      :model="searchForm"
      rowKey="id"
      :row-selection="rowSelection"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <a-tooltip placement="right">
            <template #title>
              {{ record.description }}
            </template>
            <a>{{ record.name }}</a>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="handleSelect(record)">选择</a-button>
        </template>
      </template>
    </manage-base-table>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, defineEmits, computed } from "vue";
  import type { Key } from "ant-design-vue/es/table/interface";
  import type { TableColumnProps } from "ant-design-vue";

  // 定义物料项类型
  // interface MaterielItem {
  //   id: number;
  //   name: string;
  //   code: string;
  //   model: string;
  //   unit: string;
  //   sepc: string;
  //   description?: string;
  //   stock?: number;
  //   onway_stock?: number;
  // }

  const emit = defineEmits(["selected"]);
  const visible = defineModel("visible", { default: false });
  const tableRef = ref();
  const props = defineProps({
    multiple: {
      type: Boolean,
      default: false,
    },
  });

  const dataSource = ref<MaterielListItem[]>([]);
  const selectedRowKeys = ref<Key[]>([]);
  const selectedItems = ref<MaterielListItem[]>([]);

  const query = useApiTrpc().admin.materiel.queryMateriel.query;

  const searchForm = ref({
    name: undefined,
    code: undefined,
    model: undefined,
    skuCode: undefined,
  });

  const columns: TableColumnProps<MaterielListItem>[] = [
    { title: "物料名称", dataIndex: "name" },
    { title: "物料编码", dataIndex: "code" },
    { title: "型号", dataIndex: "model" },
    { title: "单位", dataIndex: "unit" },
    { title: "规格", dataIndex: "sepc" },
    { title: "库存", dataIndex: "stock" },
    { title: "在途库存", dataIndex: "onway_stock" },
    { title: "操作", dataIndex: "action" },
  ];

  // 配置行选择
  const rowSelection = computed(() => ({
    type: props.multiple ? "checkbox" : "radio",
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys: Key[], rows: MaterielListItem[]) => {
      selectedRowKeys.value = keys;
      selectedItems.value = rows;
    },
  }));

  const handleSelect = (record: MaterielListItem) => {
    // 将当前行添加到选中项
    if (props.multiple) {
      // 多选模式：检查是否已选择，如果没有则添加
      const index = selectedItems.value.findIndex(
        (item) => item.id === record.id
      );
      if (index === -1) {
        selectedItems.value.push(record);
        selectedRowKeys.value.push(record.id);
      }
    } else {
      // 单选模式：替换选中项并关闭窗口
      selectedItems.value = [record];
      selectedRowKeys.value = [record.id];
      // 单选模式下直接确认并关闭窗口
      handleOk();
    }
  };

  const pagination = ref({
    current: 1,
    pageSize: 5,
    total: 0,
  });

  const handleSearch = () => {
    // 实现搜索逻辑
    console.log("搜索条件:", searchForm.value);
    tableRef.value.query();
  };

  const handleOk = () => {
    if (selectedItems.value.length === 0) {
      return;
    }
    emit("selected", selectedItems.value);
    visible.value = false;
    // 重置选择
    selectedRowKeys.value = [];
    selectedItems.value = [];
  };

  const handleCancel = () => {
    visible.value = false;
    // 重置选择
    selectedRowKeys.value = [];
    selectedItems.value = [];
  };

  watch(
    () => visible.value,
    async (val) => {
      nextTick(() => {
        if (val) {
          tableRef.value.query();
          // 重置选择
          selectedRowKeys.value = [];
          selectedItems.value = [];
        }
      });
    },
    { immediate: false }
  );
</script>
