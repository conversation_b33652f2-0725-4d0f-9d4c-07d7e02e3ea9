<template>
  <div class="order-form">
    <a-spin :spinning="loading">
      <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical">
        <!-- 基本信息部分 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="supplierId" label="供应商" :required="true">
              <a-select
                v-model:value="formState.supplierId"
                placeholder="请选择供应商"
                :options="supplierOptions"
                :fieldNames="{ label: 'name', value: 'id' }"
                :disabled="isView"
                show-search
                :filter-option="filterSupplier"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="expectedDeliveryDate" label="预计到货日期">
              <a-date-picker
                v-model:value="formState.expectedDeliveryDate"
                placeholder="请选择预计到货日期"
                :disabled="isView"
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item name="warehouseId" label="送至仓库">
          <manage-warehouse-selector v-model:value="formState.warehouseId" />
        </a-form-item>
        <a-form-item name="note" label="备注">
          <a-textarea
            v-model:value="formState.note"
            placeholder="请输入备注信息"
            :rows="2"
            :disabled="isView"
          />
        </a-form-item>

        <!-- 物料列表部分 -->
        <a-divider>物料列表</a-divider>

        <a-form-item
          name="items"
          :rules="[{ required: true, message: '请至少添加一项物料' }]"
        >
          <manage-materiel-form-table
            :source="formState.items"
            :has-note="false"
            :has-price="true"
            :has-delete="true"
          ></manage-materiel-form-table>
        </a-form-item>

        <!-- 表单操作按钮 -->
        <div class="form-actions">
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              v-if="!isView"
              type="primary"
              @click="handleSubmit"
              :loading="submitting"
            >
              保存
            </a-button>
          </a-space>
        </div>
      </a-form>
    </a-spin>

    <!-- 批量添加物料模态框 -->
    <manage-materiel-modelselector
      @selected="onBatchMaterielSelect"
      :multiple="true"
      v-model:visible="materielModalOpen"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch, nextTick } from "vue";
  import { message } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue";
  import { PlusOutlined } from "@ant-design/icons-vue";
  import dayjs from "dayjs";
  import type { Dayjs } from "dayjs";
  import { v4 as uuidv4 } from "uuid";
  import MaterielSelector from "~/components/item/materiel/selector.client.vue";
  import ManageMaterielModelselector from "~/components/manage/materiel/modelselector.client.vue";

  // 定义组件属性
  const props = defineProps({
    orderData: {
      type: Object,
      default: () => ({}),
    },
    mode: {
      type: String,
      default: "add",
      validator: (value: string) => ["add", "edit", "view"].includes(value),
    },
  });

  // 定义事件
  const emit = defineEmits(["success", "cancel"]);

  // 计算属性
  const isView = computed(() => props.mode === "view");
  const isEdit = computed(() => props.mode === "edit");

  const materielModalOpen = ref(false);

  // 表单引用
  const formRef = ref<FormInstance>();

  // 状态变量
  const loading = ref(false);
  const submitting = ref(false);
  const supplierOptions = ref<any[]>([]);
  const materialOptions = ref<any[]>([]);
  const materialMap = ref<Map<number, any>>(new Map());
  const totalAmount = ref(0);

  // 表单状态
  const formState = reactive({
    supplierId: undefined as number | undefined,
    expectedDeliveryDate: undefined as Dayjs | undefined,
    warehouseId: undefined as number | undefined,
    note: "",
    items: [] as any[],
  });

  // 表单验证规则
  const rules = {
    supplierId: [{ required: true, message: "请选择供应商" }],
    items: [{ required: true, message: "请至少添加一项物料" }],
  };

  // 表格列定义
  const materialColumns = [
    {
      title: "物料名称",
      dataIndex: "name",
      key: "name",
      width: "20%",
    },
    { title: "规格", dataIndex: "spec", key: "spec", width: "10%" },
    { title: "单位", dataIndex: "unit", key: "unit", width: "8%" },
    { title: "数量", dataIndex: "quantity", key: "quantity", width: "12%" },
    { title: "单价", dataIndex: "unitPrice", key: "unitPrice", width: "12%" },
    { title: "总价", dataIndex: "totalPrice", key: "totalPrice", width: "12%" },
    // { title: "备注", dataIndex: "note", key: "note", width: "16%" },
    { title: "操作", key: "action", width: "10%" },
  ];

  // 格式化金额
  const formatCurrency = (amount: number) => {
    if (isNaN(amount)) return "¥ 0.00";
    return `¥ ${amount.toFixed(2)}`;
  };

  // 过滤供应商选项
  const filterSupplier = (inputValue: string, option: any) => {
    return option.name.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
  };

  // 过滤物料选项
  const filterMaterial = (inputValue: string, option: any) => {
    return option.name.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
  };

  // 获取物料信息
  const getMaterialInfo = (materialId: number) => {
    return materialMap.value.get(materialId);
  };

  // 新增变量
  const selectedMaterialId = ref<number>(0);

  // 处理物料选择
  const handleMaterialSelected = (id: number) => {
    if (!id) return;

    const material = materialMap.value.get(id);
    if (!material) return;

    // 添加物料到列表
    formState.items.push({
      uid: uuidv4(),
      materialId: id,
      quantity: 1,
      unitPrice: material.defaultPrice || 0,
      note: "",
    });

    // 计算总金额
    calculateTotalAmount();

    // 重置选择状态
    selectedMaterialId.value = 0;
  };

  // 处理物料选择变更
  const handleMaterialChange = (materialId: any, index: number) => {
    // 确保materialId是数字类型
    const numericMaterialId = Number(materialId);
    if (isNaN(numericMaterialId)) return;

    const material = materialMap.value.get(numericMaterialId);
    if (material && material.defaultPrice) {
      formState.items[index].unitPrice = material.defaultPrice;
    }
    calculateTotalAmount();
  };

  // 添加物料行
  const addMaterial = () => {
    formState.items.push({
      uid: uuidv4(), // 临时ID，用于Vue的key
      materialId: undefined,
      quantity: 1,
      unitPrice: 0,
      note: "",
    });
  };

  // 添加批量添加物料的方法
  const addMaterialBatch = () => {
    materielModalOpen.value = true;
  };

  // 批量物料选择响应
  const onBatchMaterielSelect = (items: any[]) => {
    if (!items || items.length === 0) return;

    items.forEach((item) => {
      // 添加到表格
      formState.items.push({
        uid: uuidv4(),
        materialId: item.id,
        name: item.name,
        quantity: 1,
        unitPrice: 0, // 默认单价为0
        note: "",
      });

      // 将物料信息添加到映射表，以便在表格中显示
      if (!materialMap.value.has(item.id)) {
        materialMap.value.set(item.id, {
          id: item.id,
          name: item.name,
          spec: item.sepc, // 注意这里是sepc而不是spec
          unit: item.unit,
          defaultPrice: 0,
        });
      }
    });

    calculateTotalAmount();
    materielModalOpen.value = false;
  };

  // 移除物料行
  const removeMaterial = (index: number) => {
    formState.items.splice(index, 1);
    calculateTotalAmount();
  };

  // 计算订单总金额
  const calculateTotalAmount = () => {
    totalAmount.value = formState.items.reduce((sum, item) => {
      const quantity = Number(item.quantity) || 0;
      const unitPrice = Number(item.unitPrice) || 0;
      return sum + quantity * unitPrice;
    }, 0);
  };

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const result = await useApiTrpc().admin.purchase.querySupplier.query({
        skip: 0,
        take: 1000,
      });
      if (result.code === 200) {
        supplierOptions.value = result.data.result.filter(
          (item: any) => !item.lock
        );
      }
    } catch (error) {
      console.error("获取供应商列表失败", error);
      message.error("获取供应商列表失败");
    }
  };

  // 获取物料列表
  const fetchMaterials = async () => {
    try {
      // 调用物料查询接口
      const result = await useApiTrpc().admin.materiel.queryMateriel.query({
        skip: 0,
        take: 1000,
      });
      if (result.code === 200) {
        materialOptions.value = result.data.result;
        // 建立物料映射，方便查询
        materialOptions.value.forEach((item) => {
          materialMap.value.set(item.id, item);
        });
      }
    } catch (error) {
      console.error("获取物料列表失败", error);
      message.error("获取物料列表失败");
    }
  };

  // 初始化表单数据
  const initFormData = () => {
    if (isEdit.value && props.orderData) {
      formState.supplierId = props.orderData.supplierId;
      formState.note = props.orderData.note || "";

      if (props.orderData.expectedDeliveryDate) {
        formState.expectedDeliveryDate = dayjs(
          props.orderData.expectedDeliveryDate
        );
      }

      // 初始化物料列表
      if (props.orderData.items && props.orderData.items.length > 0) {
        formState.items = props.orderData.items.map((item: any) => ({
          uid: uuidv4(),
          id: item.id, // 保存原ID，用于更新
          materialId: item.materialId,
          quantity: item.quantity,
          price: item.price,
          note: item.note || "",
        }));
      }
    } else {
      // 添加一个空行
      // addMaterial();
    }

    // 计算总金额
    nextTick(() => {
      calculateTotalAmount();
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      submitting.value = true;

      // 构建提交数据
      const submitData = {
        supplierId: formState.supplierId as number,
        expectedDeliveryDate: formState.expectedDeliveryDate
          ? formState.expectedDeliveryDate.format("YYYY-MM-DD")
          : undefined,
        note: formState.note,
        warehouseId: formState.warehouseId as number,
        items: formState.items.map((item) => ({
          id: item.id, // 如果是编辑模式，会有id
          materialId: item.materialId,
          quantity: Number(item.quantity),
          price: Number(item.price),
          note: item.note,
        })),
      };

      let result;

      if (isEdit.value) {
        // 更新订单
        result = await useApiTrpc().admin.purchase.updatePurchaseOrder.mutate({
          id: props.orderData.id,
          ...submitData,
        });
      } else {
        // 创建订单
        result = await useApiTrpc().admin.purchase.createPurchaseOrder.mutate(
          submitData
        );
      }

      if (result.code === 200) {
        message.success(isEdit.value ? "更新订单成功" : "创建订单成功");
        emit("success");
      } else {
        message.error(
          result.message || (isEdit.value ? "更新订单失败" : "创建订单失败")
        );
      }
    } catch (error) {
      console.error("表单提交失败", error);
      message.error("表单验证失败，请检查输入");
    } finally {
      submitting.value = false;
    }
  };

  // 取消操作
  const handleCancel = () => {
    emit("cancel");
  };

  // 初始化
  onMounted(async () => {
    loading.value = true;
    try {
      // 并行加载数据
      await Promise.all([fetchSuppliers(), fetchMaterials()]);
      initFormData();
    } finally {
      loading.value = false;
    }
  });
</script>

<style scoped>
  .order-form {
    padding: 0 10px;
  }

  .form-actions {
    margin-top: 24px;
    text-align: right;
  }

  .add-item {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .total-amount {
    text-align: right;
    margin-top: 16px;
    font-size: 16px;
    font-weight: bold;
  }

  .amount {
    color: #1890ff;
    margin-left: 8px;
  }

  :deep(.no-margin) {
    margin-bottom: 0;
  }
</style>
