-- CreateTable
CREATE TABLE `DisinfectionRecord` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `materiel_id` INTEGER NOT NULL,
    `quantity` DECIMAL(20, 4) NOT NULL,
    `batch_no` VARCHAR(255) NOT NULL,
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `DisinfectionRecord` ADD CONSTRAINT `DisinfectionRecord_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
