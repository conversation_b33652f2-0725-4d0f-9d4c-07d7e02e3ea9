<template>
  <a-select
    v-model:value="value"
    show-search
    placeholder="请选择"
    :options="options"
    :filter-option="filterOption"
    @focus="handleFocus"
    @blur="handleBlur"
    @change="handleChange"
  ></a-select>
</template>
<script lang="ts" setup>
  import type { SelectProps } from "ant-design-vue";
  import { ref } from "vue";
  const options = ref<SelectProps["options"]>([]);
  onMounted(async () => {
    // const { data } = await useApi("/api/usermanage/role", { method: "get" });
    const { data } = await useApiFetch.queryUserRoleList();
    options.value = data.result.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  });
  const queryRole = async () => {};
  const handleChange = (value: any) => {
    console.log(`selected ${value}`);
  };
  const handleBlur = () => {
    console.log("blur");
  };
  const handleFocus = async () => {
    console.log("focus");
    // const data = await useApi().get()("/api/usernamage/list");
  };
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const value = defineModel<number | undefined>("value", {
    required: true,
  });
  // const value = ref<string | undefined>(undefined);
</script>
