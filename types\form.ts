import type { FormProps } from "ant-design-vue";
import type { ZodSchema, ZodError } from "zod";

export interface ZodFormProps<T extends ZodSchema> {
  schema: T;
  initialValues?: Partial<T["_output"]>;
  onSubmit?: (values: T["_output"]) => void | Promise<void>;
  layout?: FormProps["layout"];
  disabled?: boolean;
}

export interface ZodFormInstance<T extends ZodSchema> {
  submit: () => Promise<void>;
  reset: () => void;
  validate: () => Promise<T["_output"]>;
  setFieldValue: (name: keyof T["_output"], value: any) => void;
  setFieldError: (name: keyof T["_output"], error: string) => void;
  clearFieldError: (name: keyof T["_output"]) => void;
  formData: Partial<T["_output"]>;
  errors: Partial<Record<keyof T["_output"], string>>;
}
