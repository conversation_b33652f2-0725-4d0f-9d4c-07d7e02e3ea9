<template>
  <div class="menu-manager">
    <a-card title="菜单管理">
      <template #extra>
        <a-button type="primary" @click="handleSyncMenus">
          <template #icon><sync-outlined /></template>
          同步菜单
        </a-button>
      </template>

      <a-table
        :columns="columns"
        :data-source="menuList"
        :loading="loading"
        :pagination="false"
        :childrenColumnName="'children'"
        :row-key="(record) => record.id"
      >
        <template #bodyCell="{ column, record }">
          <!-- 图标列 -->
          <template v-if="column.key === 'icon'">
            <a-input v-model:value="record.icon" placeholder="请输入图标名称" />
          </template>

          <!-- 排序列 -->
          <template v-if="column.key === 'sort'">
            <a-input-number
              v-model:value="record.sort"
              :min="0"
              @change="handleSortChange(record as MenuRecord)"
            />
          </template>

          <!-- 隐藏列 -->
          <template v-if="column.key === 'hidden'">
            <a-switch
              v-model:checked="record.hidden"
              @change="handleHiddenChange(record as MenuRecord)"
            />
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleSave(record as MenuRecord)"
                >保存</a-button
              >
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { SyncOutlined } from "@ant-design/icons-vue";
  import type { ColumnType } from "ant-design-vue/es/table";
  import type { MenuRecord } from "~/types/menu";

  // 定义表格列
  const columns: ColumnType<MenuRecord>[] = [
    {
      title: "菜单名称",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "路径",
      dataIndex: "path",
      key: "path",
      width: 200,
    },
    {
      title: "图标",
      dataIndex: "icon",
      key: "icon",
      width: 150,
    },
    {
      title: "排序",
      dataIndex: "sort",
      key: "sort",
      width: 100,
    },
    {
      title: "隐藏",
      dataIndex: "hidden",
      key: "hidden",
      width: 100,
    },
    {
      title: "组件路径",
      dataIndex: "component",
      key: "component",
    },
    {
      title: "操作",
      key: "action",
      width: 120,
    },
  ];

  // 加载菜单数据
  const loading = ref(false);
  const menuList = ref<MenuRecord[]>([]);

  const { $client } = useNuxtApp();

  const loadMenus = async () => {
    loading.value = true;
    try {
      const data = await $client.query("menu.item", {});
      menuList.value =
        data.map((item: any) => ({
          ...item,
          icon: item.icon ?? undefined,
          component: item.component ?? undefined,
        })) || [];
    } finally {
      loading.value = false;
    }
  };

  // 同步菜单
  const handleSyncMenus = async () => {
    try {
      await $fetch("/api/menus/sync", { method: "POST" });
      message.success("菜单同步成功");
      await loadMenus();
    } catch (error) {
      message.error("菜单同步失败");
    }
  };

  // 保存菜单
  const handleSave = async (record: MenuRecord) => {
    try {
      await $fetch(`/api/menus/${record.id}`, {
        method: "PUT",
        body: record,
      });
      message.success("保存成功");
      await loadMenus();
    } catch (error) {
      message.error("保存失败");
    }
  };

  // 排序变更
  const handleSortChange = async (record: MenuRecord) => {
    await handleSave(record);
  };

  // 隐藏状态变更
  const handleHiddenChange = async (record: MenuRecord) => {
    await handleSave(record);
  };

  // 初始加载
  onMounted(() => {
    loadMenus();
  });
</script>

<style scoped>
  .menu-manager {
    padding: 24px;
  }
</style>
