<template>
  <a-modal
    v-model:open="visible"
    title="选择"
    @ok="handleOk"
    @cancel="handleCancel"
    width="80%"
  >
    <a-form layout="inline">
      <a-form-item label="物料名称">
        <a-input
          v-model:value="searchForm.title"
          placeholder="请输入物料名称"
        />
      </a-form-item>
      <a-form-item label="物料编码">
        <a-input v-model:value="searchForm.code" placeholder="请输入物料编码" />
      </a-form-item>
      <a-form-item label="SKU编码">
        <a-input
          v-model:value="searchForm.skuCode"
          placeholder="请输入SKU编码"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">搜索</a-button>
      </a-form-item>
    </a-form>
    <a-row :gutter="16">
      <a-col :span="props.multiple ? 15 : 24">
        <a-divider orientation="left">请选择</a-divider>
        <ui-storemanage-table
          ref="tableRef"
          :columns="columns"
          :query="useApiFetch.queryMaterielWithSku"
          :model="searchForm"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a @click="handleSelect(record)">选择</a>
            </template>
          </template>
        </ui-storemanage-table>
      </a-col>
      <a-col :span="9" v-if="props.multiple">
        <a-divider orientation="left">已选择</a-divider>
        <a-list
          size="small"
          bordered
          :data-source="selectedItems"
          style="max-height: calc(100vh - 400px); overflow-y: auto"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" danger @click="removeSelected(item)"
                  >移除</a-button
                >
              </template>
              <a-list-item-meta>
                <template #title>
                  {{ item.Product.title }}({{ item.Product.code }})-{{
                    item.Product.model
                  }}
                </template>
                <template #description
                  >{{ item.title }}-{{ item.unit }}-{{
                    item.specification
                  }}</template
                >
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, defineEmits } from "vue";
  import type { Key } from "ant-design-vue/es/table/interface";

  const emit = defineEmits(["selected"]);
  const visible = defineModel("visible", { default: false });
  const tableRef = ref();
  interface MaterielItem {
    id: number;
    title: string;
    unit: string;
    specification: string;
    Product: {
      id: number;
      title: string;
      code: string;
      model: string | null;
    };
  }
  const props = defineProps({
    multiple: {
      type: Boolean,
      default: false,
    },
  });
  const dataSource = ref<MaterielItem[]>([]);

  const searchForm = ref({
    title: "",
    code: "",
    skuCode: "",
  });

  const columns = [
    {
      title: "物料名称",
      customRender: ({ record }: { record: MaterielItem }) =>
        record.Product.title,
    },
    {
      title: "物料编码",
      customRender: ({ record }: { record: MaterielItem }) =>
        record.Product.code,
    },
    {
      title: "型号",
      customRender: ({ record }: { record: MaterielItem }) =>
        record.Product.model,
    },
    {
      title: "SKU名称",
      customRender: ({ record }: { record: MaterielItem }) => record.title,
    },
    {
      title: "单位",
      customRender: ({ record }: { record: MaterielItem }) => record.unit,
    },
    {
      title: "规格",
      customRender: ({ record }: { record: MaterielItem }) =>
        record.specification,
    },
    { title: "操作", key: "action" },
  ];

  const pagination = ref({
    current: 1,
    pageSize: 5,
    total: 0,
  });

  const handleSearch = () => {
    // 实现搜索逻辑
    console.log("搜索条件:", searchForm.value);
    tableRef.value.query();
  };

  const handleSelect = (record: any) => {
    // visible.value = false;
    selectedItems.value.push(record);
    // 避免重复选择
    selectedItems.value = [...new Set(selectedItems.value)];
    if (!props.multiple) {
      handleOk();
    }
  };

  const handleOk = () => {
    if (selectedItems.value.length === 0) {
      return;
    }
    emit("selected", selectedItems.value);
    console.log(selectedItems.value);
    visible.value = false;
    selectedItems.value = [];
  };

  const handleCancel = () => {
    visible.value = false;
    selectedItems.value = [];
  };

  const selectedItems = ref<MaterielItem[]>([]);

  const selectedColumns = [
    { title: "物料名称", dataIndex: "title" },
    { title: "物料编码", dataIndex: "code" },
    { title: "操作", key: "action" },
  ];

  const removeSelected = (record: any) => {
    const index = selectedItems.value.findIndex(
      (item) => item.id === record.id
    );
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    }
  };

  watch(
    () => visible.value,
    async (val) => {
      nextTick(() => {
        if (val) {
          tableRef.value.query();
        }
      });
    },
    { immediate: false }
  );
</script>
