/**
 * 封装fetch,实现前端和后端的数据交互
 */
import type { UseFetchOptions } from "nuxt/app";
import type { FetchError } from "ofetch";
import type { NitroFetchRequest } from "nitropack";
import type { Prisma } from "@prisma/client";
// const { $emitter } = useNuxtApp();

import type { TRPCLink, TRPCClientError } from "@trpc/client";
import { observable } from "@trpc/server/observable";
import { loggerLink } from "@trpc/client/links/loggerLink";
import {
  createTRPCNuxtClient,
  httpBatchLink,
  httpLink,
} from "trpc-nuxt/client";
import type { AppRouter } from "~/server/trpc/routers";
import axios from "axios";
export const useApi = $fetch.create({
  onRequest({ request, options, error }) {
    if (import.meta.server) {
      //服务端发送的Fetch请求，需要带上客户端的sessionId
      const cookie = useCookie("sessionId");
      options.headers.set("Cookie", "sessionId=" + cookie.value);
      // console.log("带了sessionId", cookie.value);
    }
  },
  onResponse({ response }) {
    if (response.status == 200) {
      switch (response._data.code) {
        case 0:
          notification.error({
            message: "请求错误",
            description: response._data.message,
          });
          // $emitter.emit("error", 0);
          break;
      }
    }
  },
  onResponseError({ response, error }) {
    if (import.meta.client) {
      switch (response.status) {
        case 400:
          notification.error({
            message: "请求参数错误",
            description: "请联系管理员处理该问题",
            duration: 0,
          });

          break;
        case 401:
          notification.error({
            message: "登录过期",
            description: "请尝试重新登录",
            duration: 0,
          });
          break;
        case 403:
          notification.error({
            message: "权限不足",
            description: "请联系管理员授权",
            duration: 0,
          });
          break;
        case 404:
          notification.error({
            message: "请求的资源不存在",
            description: "请联系管理员处理该问题",
            duration: 0,
          });
          break;
        case 500:
          notification.error({
            message: "服务器内部错误",
            description: "请稍后再试",
            duration: 0,
          });
          break;
        default:
          notification.error({
            message: "请求失败",
            description: "请稍后再试",
            duration: 0,
          });
          break;
      }
    }
  },
});

export const useApiTrpc = () => {
  /**
   * createTRPCNuxtClient adds a `useQuery` composable
   * built on top of `useAsyncData`.
   */
  const apiUrl = useRuntimeConfig().public.apiUrl as string;
  const axiosFetch: typeof fetch = async (input, init) => {
    // try {
    const axiosInstance = axios.create({
      baseURL: `${apiUrl}/api/trpc`,
    });
    //请求拦截器
    axiosInstance.interceptors.request.use();
    //响应拦截器
    axiosInstance.interceptors.response.use(
      (response) => {
        if (import.meta.client) {
          switch (response.data.result.data.code) {
            case 0:
              notification.error({
                message: "请求错误",
                description: response.data.result.data.message,
              });
              break;
          }
        }
        // 处理trpc返回的数据,将data直接返回.
        // response.data.result.data = response.data.result.data.data;
        return response;
      },
      (error) => {
        if (error.response && import.meta.client) {
          switch (error.response.status) {
            case 400:
              notification.error({
                message: "请求参数错误",
                description: "请联系管理员处理该问题",
                duration: 0,
              });
              break;
            case 401:
              notification.error({
                message: "登录过期",
                description: "请尝试重新登录",
                duration: 0,
              });
              break;
            case 403:
              notification.error({
                message: "权限不足",
                description: "请联系管理员授权",
                duration: 0,
              });
              break;
            case 404:
              notification.error({
                message: "请求的资源不存在",
                description: "请联系管理员处理该问题",
                duration: 0,
              });
              break;
            case 500:
              notification.error({
                message: "服务器内部错误",
                description: "请稍后再试",
                duration: 0,
              });
              break;
            default:
              notification.error({
                message: "请求失败",
                description: "请稍后再试",
                duration: 0,
              });
              break;
          }
        }
        return Promise.reject(error);
      }
    );
    //请求
    const response = await axiosInstance.request({
      url: input.toString(),
      method: init?.method,
      headers: init?.headers as any,
      data: init?.body,
    });

    return new Response(JSON.stringify(response.data));
    // } catch (error) {
    //   // console.log(input);
    //   // console.log(error);
    //   return new Response(JSON.stringify({ error: "请求失败" }));
    // }
  };
  const client = createTRPCNuxtClient<AppRouter>({
    links: [
      loggerLink({
        // enabled: () => process.env.NODE_ENV === "development",
        enabled: (opts) => {
          if (opts.direction === "up") {
            switch (opts.path) {
              case "public.auth.captcha":
                return false;
            }
            console.log(opts.path);
          }
          return process.env.NODE_ENT === "development";
        },
      }),
      httpLink({
        url: "/",
        fetch: axiosFetch,
      }),
    ],
  });
  return client;
};
