<script setup lang="ts">
  /**
   * 物料选择器
   */
  import ModelMateriel from "~/components/modal/materiel.client.vue";
  const materielid = defineModel<number>("materielid", {
    required: true,
  });
  const materieltitle = ref("");
  const code = ref("");
  const skutitle = ref("");
  const unit = ref("");
  const specification = ref("");
  const model = ref("");
  const handleAddMaterial = () => {
    materielModalOpen.value = true;
  };
  const materielModalOpen = ref(false);
  const onMaterielSelect = (
    item: API.MaterielSkuReturnType["data"]["result"]
  ) => {
    console.log("onMaterielSelect", item[0].Product.title);
    materieltitle.value = item[0].Product.title;
    code.value = item[0].Product.code;
    skutitle.value = item[0].title;
    unit.value = item[0].unit;
    specification.value = item[0].specification;
    materielid.value = item[0].id;
    model.value = item[0].Product.model;
  };
</script>
<template>
  <div>
    <a-button
      type="dashed"
      block
      @click="handleAddMaterial"
      v-if="materieltitle == ''"
      >选择</a-button
    >
    <a-list v-else>
      <a-list-item>
        <template #actions>
          <a-button type="link" @click="handleAddMaterial"> 重新选择 </a-button>
        </template>
        <a-list-item-meta>
          <template #title
            >{{ materieltitle }}({{ code }})-{{ model }}</template
          >
          <template #description>
            {{ skutitle }}-{{ unit }}-{{ specification }}
          </template>
        </a-list-item-meta>
      </a-list-item>
    </a-list>
    <model-materiel
      @selected="onMaterielSelect"
      v-model:open="materielModalOpen"
    />
  </div>
</template>
