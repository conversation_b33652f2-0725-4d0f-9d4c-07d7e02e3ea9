/*
  Warnings:

  - You are about to drop the column `sepc` on the `Materiel` table. All the data in the column will be lost.
  - You are about to alter the column `stock` on the `Materiel` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `onway_stock` on the `Materiel` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `lowstock` on the `Materiel` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `maxstock` on the `Materiel` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `quantity` on the `ProductionPlanItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `quantity` on the `ProductionTask` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `completed_quantity` on the `ProductionTask` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `qualified_quantity` on the `ProductionTask` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `unqualified_quantity` on the `ProductionTask` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,0)` to `Decimal(20,4)`.
  - You are about to alter the column `totalAmount` on the `PurchaseOrder` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(20,4)`.
  - You are about to alter the column `quantity` on the `PurchaseOrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(20,4)`.
  - You are about to alter the column `unitPrice` on the `PurchaseOrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(20,4)`.
  - You are about to alter the column `receivedQuantity` on the `PurchaseReceiptItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(20,4)`.
  - You are about to alter the column `quantity` on the `Stock` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,2)` to `Decimal(20,4)`.
  - You are about to drop the column `lock` on the `User` table. All the data in the column will be lost.
  - You are about to drop the `BomItem` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `BomItem` DROP FOREIGN KEY `BomItem_materiel_id_fkey`;

-- AlterTable
ALTER TABLE `Materiel` DROP COLUMN `sepc`,
    ADD COLUMN `specification` VARCHAR(255) NULL,
    MODIFY `stock` DECIMAL(20, 4) NOT NULL DEFAULT 0,
    MODIFY `onway_stock` DECIMAL(20, 4) NOT NULL DEFAULT 0,
    MODIFY `lowstock` DECIMAL(20, 4) NOT NULL DEFAULT 0,
    MODIFY `maxstock` DECIMAL(20, 4) NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `ProductionPlanItem` MODIFY `quantity` DECIMAL(20, 4) NOT NULL;

-- AlterTable
ALTER TABLE `ProductionTask` MODIFY `quantity` DECIMAL(20, 4) NOT NULL,
    MODIFY `completed_quantity` DECIMAL(20, 4) NOT NULL DEFAULT 0,
    MODIFY `qualified_quantity` DECIMAL(20, 4) NOT NULL DEFAULT 0,
    MODIFY `unqualified_quantity` DECIMAL(20, 4) NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `PurchaseOrder` MODIFY `totalAmount` DECIMAL(20, 4) NOT NULL;

-- AlterTable
ALTER TABLE `PurchaseOrderItem` MODIFY `quantity` DECIMAL(20, 4) NOT NULL,
    MODIFY `unitPrice` DECIMAL(20, 4) NOT NULL;

-- AlterTable
ALTER TABLE `PurchaseReceiptItem` MODIFY `receivedQuantity` DECIMAL(20, 4) NOT NULL;

-- AlterTable
ALTER TABLE `Stock` MODIFY `quantity` DECIMAL(20, 4) NOT NULL;

-- AlterTable
ALTER TABLE `User` DROP COLUMN `lock`,
    ADD COLUMN `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active';

-- DropTable
DROP TABLE `BomItem`;

-- CreateTable
CREATE TABLE `MaterielBom` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `parentId` INTEGER NOT NULL,
    `childId` INTEGER NOT NULL,
    `quantity` DOUBLE NOT NULL,
    `remark` VARCHAR(191) NULL,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updateAt` DATETIME(3) NOT NULL,

    INDEX `MaterielBom_parentId_idx`(`parentId`),
    INDEX `MaterielBom_childId_idx`(`childId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProductionOutRecord` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `productionTaskId` INTEGER NOT NULL,
    `materiel_id` INTEGER NOT NULL,
    `quantity` DECIMAL(20, 4) NOT NULL,
    `note` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserFaceFeature` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `faceDescriptor` TEXT NOT NULL,
    `faceImage` TEXT NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserFaceFeature_userId_key`(`userId`),
    INDEX `UserFaceFeature_userId_idx`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `MaterielBom` ADD CONSTRAINT `MaterielBom_parentId_fkey` FOREIGN KEY (`parentId`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `MaterielBom` ADD CONSTRAINT `MaterielBom_childId_fkey` FOREIGN KEY (`childId`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionOutRecord` ADD CONSTRAINT `ProductionOutRecord_productionTaskId_fkey` FOREIGN KEY (`productionTaskId`) REFERENCES `ProductionTask`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProductionOutRecord` ADD CONSTRAINT `ProductionOutRecord_materiel_id_fkey` FOREIGN KEY (`materiel_id`) REFERENCES `Materiel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFaceFeature` ADD CONSTRAINT `UserFaceFeature_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
