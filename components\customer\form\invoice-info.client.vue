<template>
  <manage-base-modalform
    v-model:visible="visible"
    v-model:loading="loading"
    :title="formState.id ? '编辑发票信息' : '添加发票信息'"
    :model="formState"
    @submit="handleSubmit"
  >
    <template #default="{ show }">
      <slot :show="show"></slot>
    </template>
    <template #form>
      <a-form-item
        label="发票抬头"
        name="title"
        :rules="[{ required: true, message: '请输入发票抬头' }]"
      >
        <a-input v-model:value="formState.title" placeholder="请输入发票抬头" />
      </a-form-item>
      <a-form-item
        label="税号"
        name="taxNumber"
        :rules="[{ required: true, message: '请输入税号' }]"
      >
        <a-input v-model:value="formState.taxNumber" placeholder="请输入税号" />
      </a-form-item>
      <a-form-item label="开户银行" name="bankName">
        <a-input v-model:value="formState.bankName" placeholder="请输入开户银行" />
      </a-form-item>
      <a-form-item label="银行账号" name="bankAccount">
        <a-input v-model:value="formState.bankAccount" placeholder="请输入银行账号" />
      </a-form-item>
      <a-form-item label="地址" name="address">
        <a-input v-model:value="formState.address" placeholder="请输入地址" />
      </a-form-item>
      <a-form-item label="电话" name="tel">
        <a-input v-model:value="formState.tel" placeholder="请输入电话" />
      </a-form-item>
      <a-form-item
        label="发票类型"
        name="invoiceType"
        :rules="[{ required: true, message: '请选择发票类型' }]"
      >
        <a-select v-model:value="formState.invoiceType" placeholder="请选择发票类型">
          <a-select-option value="common">普通发票</a-select-option>
          <a-select-option value="special">专用发票</a-select-option>
          <a-select-option value="electronic">电子发票</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </manage-base-modalform>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';

// 发票类型枚举
type InvoiceType = 'common' | 'special' | 'electronic';

// 表单状态
interface InvoiceInfoForm {
  id?: number;
  customer_id: number;
  title: string;
  taxNumber: string;
  bankName?: string;
  bankAccount?: string;
  address?: string;
  tel?: string;
  invoiceType: InvoiceType;
}

// 表单状态
const formState = reactive<InvoiceInfoForm>({
  customer_id: 0,
  title: '',
  taxNumber: '',
  bankName: '',
  bankAccount: '',
  address: '',
  tel: '',
  invoiceType: 'common',
});

// 表单状态
const visible = ref(false);
const loading = ref(false);

// TRPC接口引用
const trpc = useApiTrpc();
const addInvoiceInfoMutation = trpc.admin.customer.addCustomerInvoiceInfo.mutate;
const updateInvoiceInfoMutation = trpc.admin.customer.updateCustomerInvoiceInfo.mutate;

// 提交表单
const handleSubmit = async () => {
  try {
    if (formState.id) {
      // 更新
      await updateInvoiceInfoMutation({
        id: formState.id,
        title: formState.title,
        taxNumber: formState.taxNumber,
        bankName: formState.bankName,
        bankAccount: formState.bankAccount,
        address: formState.address,
        tel: formState.tel,
        invoiceType: formState.invoiceType,
      });
      message.success('更新发票信息成功');
    } else {
      // 新增
      await addInvoiceInfoMutation({
        customer_id: formState.customer_id,
        title: formState.title,
        taxNumber: formState.taxNumber,
        bankName: formState.bankName,
        bankAccount: formState.bankAccount,
        address: formState.address,
        tel: formState.tel,
        invoiceType: formState.invoiceType,
      });
      message.success('添加发票信息成功');
    }
    visible.value = false;
    loading.value = false;
    emits('saveSuccess');
    // 重置表单
    resetForm();
  } catch (error) {
    loading.value = false;
    message.error(formState.id ? '更新发票信息失败' : '添加发票信息失败');
    console.error(error);
  }
};

// 重置表单
const resetForm = () => {
  formState.id = undefined;
  formState.customer_id = 0;
  formState.title = '';
  formState.taxNumber = '';
  formState.bankName = '';
  formState.bankAccount = '';
  formState.address = '';
  formState.tel = '';
  formState.invoiceType = 'common';
};

// 显示表单
const show = (record: InvoiceInfoForm) => {
  resetForm();
  Object.assign(formState, record);
  visible.value = true;
};

// 定义事件
const emits = defineEmits<{
  (e: 'saveSuccess'): void;
}>();

// 暴露方法
defineExpose({
  show,
});
</script>
