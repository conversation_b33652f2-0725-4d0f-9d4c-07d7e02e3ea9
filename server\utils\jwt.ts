import jwt from "jsonwebtoken";
export function useJwt() {
  const secret = "uarlab&nngene";

  function sign(payload: any, expiresIn: string = "1d") {
    if (!payload) throw new Error();
    return jwt.sign(payload, secret, {
      algorithm: "HS256",
      expiresIn: expiresIn,
    });
  }
  function verify(token: string) {
    try {
      return jwt.verify(token, secret) as API.LoginReturnType["data"];
    } catch (error) {
      // console.log(error);
      return undefined;
    }
  }
  return { sign, verify };
}
