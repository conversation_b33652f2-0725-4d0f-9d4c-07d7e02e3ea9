<template>
  <NuxtLayout :name="layout">
    <template #header>
      <a-space>
        <a-button @click="toggleFullscreen()">
          <FullscreenOutlined />全屏显示
        </a-button>
        <a-button @click="goHome"> <HomeFilled />欢迎,{{ name }} </a-button>
        <a-button @click="updatePwd()"> <EditFilled />修改密码 </a-button>
        <a-button @click="logout()"> <LogoutOutlined />退出登录 </a-button>
      </a-space>
    </template>
    <template #menu>
      <a-extract-style>
        <a-menu
          id="leftmenu"
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          @click="handleClick"
          style="border-radius: 8px"
          :disabledOverflow="true"
        >
          <slot name="menuItem"></slot>
        </a-menu>
      </a-extract-style>
    </template>
    <template #breadcrumb v-if="breadcrumb.length > 0">
      <a-breadcrumb>
        <a-breadcrumb-item>当前位置:</a-breadcrumb-item>
        <a-breadcrumb-item v-for="(item, index) in breadcrumb" :key="index">{{
          item
        }}</a-breadcrumb-item>
      </a-breadcrumb>
    </template>
    <slot></slot>
  </NuxtLayout>
</template>
<script lang="ts" setup>
  import {
    MailOutlined,
    AppstoreOutlined,
    SettingOutlined,
  } from "@ant-design/icons-vue";
  import type { CSSProperties } from "vue";
  import {
    HomeFilled,
    EditFilled,
    LogoutOutlined,
  } from "@ant-design/icons-vue";
  import type { MenuProps, ItemType } from "ant-design-vue";
  import type { MenuInfo } from "ant-design-vue/es/menu/src/interface";
  const layout = "manage-frame";
  const emits = defineEmits<{
    goHome: [];
    logout: [];
    updatePwd: [];
    selectMenuItem: [MenuInfo];
  }>();
  const selectedKeys = defineModel<string[]>("selectedKeys", {
    default: () => ["1"],
  });
  const breadcrumb = defineModel<string[]>("breadcrumb", {
    default: () => [],
  });
  //   const openKeys = ref<string[]>(["sub1"]);

  const handleClick: MenuProps["onClick"] = (e) => {
    // console.log("click", e);
    emits("selectMenuItem", e);
  };

  // const user = await useApi().get()("/api/user/loginstatus");
  const user = await useAuth().getUser();
  const name = ref("");
  if (user) {
    name.value = user.name;
  }

  const activeIndex = ref("logo");
  const handleSelect = (key: any, keyPath: any) => {};

  const goHome = () => {
    emits("goHome");
  };
  const updatePwd = () => {
    emits("updatePwd");
  };
  const logout = async () => {
    // emits("logout");
    Modal.confirm({
      title: "提示",
      content: "确定要退出登录吗？",
      onOk: async () => {
        const exit = await useApiTrpc().public.auth.logout.query();
        if (exit.code) {
          navigateTo("/storemanage");
        }
      },
    });
  };
</script>
<style scoped>
  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .el-menu {
    --el-menu-bg-color: #207dc3;
    --el-menu-text-color: #fff;
    /* --el-menu-active-color: #fff; */
  }

  .el-menu--horizontal > .el-menu-item:nth-child(1) {
    margin-right: auto;
  }
</style>
