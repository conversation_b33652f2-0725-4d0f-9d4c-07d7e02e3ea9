<template>
  <manage-base-modalform
    v-model:visible="visible"
    v-model:loading="loading"
    :title="formState.id ? '编辑收货信息' : '添加收货信息'"
    :model="formState"
    @submit="handleSubmit"
  >
    <template #default="{ show }">
      <slot :show="show"></slot>
    </template>
    <template #form>
      <a-form-item
        label="收货人姓名"
        name="recName"
        :rules="[{ required: true, message: '请输入收货人姓名' }]"
      >
        <a-input v-model:value="formState.recName" placeholder="请输入收货人姓名" />
      </a-form-item>
      <a-form-item
        label="收货地址"
        name="recAddress"
        :rules="[{ required: true, message: '请输入收货地址' }]"
      >
        <a-textarea
          v-model:value="formState.recAddress"
          placeholder="请输入收货地址"
          :rows="3"
        />
      </a-form-item>
      <a-form-item
        label="联系电话"
        name="recTel"
        :rules="[{ required: true, message: '请输入联系电话' }]"
      >
        <a-input v-model:value="formState.recTel" placeholder="请输入联系电话" />
      </a-form-item>
    </template>
  </manage-base-modalform>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';

// 表单状态
interface ReceivingInfoForm {
  id?: number;
  customer_id: number;
  recName: string;
  recAddress: string;
  recTel: string;
}

// 表单状态
const formState = reactive<ReceivingInfoForm>({
  customer_id: 0,
  recName: '',
  recAddress: '',
  recTel: '',
});

// 表单状态
const visible = ref(false);
const loading = ref(false);

// TRPC接口引用
const trpc = useApiTrpc();
const addReceivingInfoMutation = trpc.admin.customer.addCustomerReceivingInfo.mutate;
const updateReceivingInfoMutation = trpc.admin.customer.updateCustomerReceivingInfo.mutate;

// 提交表单
const handleSubmit = async () => {
  try {
    if (formState.id) {
      // 更新
      await updateReceivingInfoMutation({
        id: formState.id,
        recName: formState.recName,
        recAddress: formState.recAddress,
        recTel: formState.recTel,
      });
      message.success('更新收货信息成功');
    } else {
      // 新增
      await addReceivingInfoMutation({
        customer_id: formState.customer_id,
        recName: formState.recName,
        recAddress: formState.recAddress,
        recTel: formState.recTel,
      });
      message.success('添加收货信息成功');
    }
    visible.value = false;
    loading.value = false;
    emits('saveSuccess');
    // 重置表单
    resetForm();
  } catch (error) {
    loading.value = false;
    message.error(formState.id ? '更新收货信息失败' : '添加收货信息失败');
    console.error(error);
  }
};

// 重置表单
const resetForm = () => {
  formState.id = undefined;
  formState.customer_id = 0;
  formState.recName = '';
  formState.recAddress = '';
  formState.recTel = '';
};

// 显示表单
const show = (record: ReceivingInfoForm) => {
  resetForm();
  Object.assign(formState, record);
  visible.value = true;
};

// 定义事件
const emits = defineEmits<{
  (e: 'saveSuccess'): void;
}>();

// 暴露方法
defineExpose({
  show,
});
</script>
