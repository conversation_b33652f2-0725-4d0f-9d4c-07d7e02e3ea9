import { z } from "zod";
// 生产领料
export const productionOutboundSchema = z.object({
  task_id: z.number(),
  bom_items: z.array(
    z.object({
      id: z.number(),
      batch_no: z.string(),
      quantity: z.number(),
    })
  ),
});
//新增废品处理记录
export const createWasteProcessingRecordSchema = z.object({
  materiel_id: z.number(),
  batch_no: z.string(),
  quantity: z.number(),
  warehouse_id: z.number(),
  remark: z.string(),
});

//新增消毒处理记录
export const createDisinfectionRecordSchema = z.object({
  materiel_id: z.number(),
  batch_no: z.string(),
  quantity: z.number(),
  warehouse_id: z.number(),
  remark: z.string(),
});
