<template>
  <div>
    <a-card title="生产工单管理">
      <template #extra>
        <a-button type="primary" @click="handleAdd">新建工单</a-button>
      </template>
      <manage-base-table
        ref="tableRef"
        :columns="table.columns"
        :model="table.model"
        :query="useApiFetch.queryTask"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="">编辑</a-button>
            <a-button type="link" @click="">删除</a-button>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :maskClosable="false"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="工单编号" name="code">
          <a-input v-model:value="formState.code" />
        </a-form-item>
        <a-form-item label="生产物料" name="bom_id">
          <manage-bom-selector
            v-model:id="formState.bom_id"
          ></manage-bom-selector>
        </a-form-item>
        <a-form-item label="数量" name="quantity">
          <a-input-number v-model:value="formState.quantity" />
        </a-form-item>
        <a-form-item label="开始时间" name="startAt">
          <a-date-picker
            v-model:value="formState.startAt"
            :show-time="{
              defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
            }"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="结束时间" name="endAt">
          <a-date-picker
            v-model:value="formState.endAt"
            :show-time="{
              defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
            }"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="员工" name="user_id">
          <manage-user-selector
            v-model:id="formState.user_id"
          ></manage-user-selector>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formState.description" />
        </a-form-item>
        <a-form-item label="发布状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio :value="0">草稿</a-radio>
            <a-radio :value="1">已发布</a-radio>
            <a-radio :value="2">已暂停</a-radio>
            <a-radio :value="3">已取消</a-radio>
            <a-radio :value="4">已结束</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from "vue";
  import dayjs, { Dayjs } from "dayjs";
  import type { TablePaginationConfig } from "ant-design-vue";
  import { message } from "ant-design-vue";

  const table = useTable({
    columns: [
      {
        title: "工单编号",
        dataIndex: "code",
        key: "code",
      },
      {
        title: "生产物料",
        key: "materiel",
        customRender: ({ record }: { record: API.ProductionTask }) => {
          return record.Bom.Materiel.name;
        },
      },
      {
        title: "生产数量",
        dataIndex: "quantity",
        key: "quantity",
      },
      {
        title: "开始时间",
        dataIndex: "startAt",
        key: "startAt",
        customRender: ({ record }: { record: API.ProductionTask }) => {
          return dayjs(record.startAt).format("YYYY-MM-DD HH:mm:ss");
        },
      },
      {
        title: "结束时间",
        dataIndex: "endAt",
        key: "endAt",
        customRender: ({ record }: { record: API.ProductionTask }) => {
          return dayjs(record.endAt).format("YYYY-MM-DD HH:mm:ss");
        },
      },
      {
        title: "创建时间",
        dataIndex: "createAt",
        key: "createAt",
        customRender: ({ record }: { record: API.ProductionTask }) => {
          return dayjs(record.createAt).format("YYYY-MM-DD HH:mm:ss");
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        key: "status",
      },
      {
        title: "操作",
        key: "action",
      },
    ],
  });
  const tableRef = ref();

  const modalVisible = ref(false);
  const modalTitle = ref("新建工单");
  const formRef = ref();
  const formState = reactive({
    code: "",
    bom_id: 0,
    quantity: 0,
    startAt: "",
    endAt: "",
    description: "",
    status: 0,
  }) as API.ProductionTask.Create;

  const rules = {
    title: [{ required: true, message: "请输入工单名称" }],
    bom_id: [{ required: true, message: "请选择生产物料" }],
    quantity: [{ required: true, message: "请输入数量" }],
    startAt: [{ required: true, message: "请选择开始时间" }],
    endAt: [{ required: true, message: "请选择结束时间" }],
  };

  // 新建
  const handleAdd = () => {
    modalTitle.value = "新建工单";
    modalVisible.value = true;
    formState.id = undefined;
  };

  const handleModalOk = async () => {
    const res = await useApiFetch.addTask(formState);
    console.log(res);
    tableRef.value.query();
  };

  // 取消
  const handleModalCancel = () => {
    modalVisible.value = false;
    formRef.value.resetFields();
  };

  onMounted(() => {});
</script>
