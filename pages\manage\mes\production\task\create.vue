<template>
  <a-card :title="pageTitle">
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="工单编号" name="taskNo">
        <a-input
          v-model:value="formState.taskNo"
          placeholder="系统自动生成"
          readonly
        />
      </a-form-item>

      <a-form-item label="产品名称" name="productId">
        <manage-materiel-form-input
          label="产品名称"
          name="productId"
          :disabled="isViewMode"
          v-model:value="formState.productId"
        ></manage-materiel-form-input>
      </a-form-item>
      <a-form-item label="计划数量" name="planQuantity">
        <a-input-number
          v-model:value="formState.planQuantity"
          placeholder="请输入计划数量"
          :min="1"
          style="width: 100%"
          :disabled="isViewMode"
        />
      </a-form-item>
      <a-form-item label="工单完成条件" name="finishCondition">
        <a-select
          v-model:value="formState.finishCondition"
          placeholder="请选择工单完成条件"
          style="width: 100%"
          :disabled="isViewMode"
        >
          <a-select-option value="time">当超过结束时间</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="计划开始时间" name="planStartTime">
        <a-date-picker
          v-model:value="formState.planStartTime"
          format="YYYY-MM-DD"
          style="width: 100%"
          :disabled="isViewMode"
        />
      </a-form-item>
      <a-form-item label="计划结束时间" name="planEndTime">
        <a-date-picker
          v-model:value="formState.planEndTime"
          format="YYYY-MM-DD"
          style="width: 100%"
          :disabled="isViewMode"
        />
      </a-form-item>
      <a-form-item label="生产员" name="selectedOperators">
        <UserSelector
          v-model:value="formState.selectedOperators"
          multiple
          @selected="handleOperatorSelect"
          :disabled="isViewMode"
        >
          <template #trigger="{ showModal }">
            <a-input
              v-model:value="selectedOperatorNames"
              placeholder="请选择生产员"
              @click="showModal"
              readonly
              :disabled="isViewMode"
            >
              <template #suffix>
                <UserOutlined />
              </template>
            </a-input>
          </template>
        </UserSelector>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formState.remark"
          placeholder="请输入备注信息"
          :rows="4"
          :disabled="isViewMode"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.id && !isViewMode"
        label="状态"
        name="status"
      >
        <a-select
          v-model:value="formState.status"
          placeholder="请选择状态"
          style="width: 100%"
          :disabled="isViewMode"
        >
          <a-select-option value="draft">草稿</a-select-option>
          <a-select-option value="pending">待生产</a-select-option>
          <a-select-option value="in_progress">生产中</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
          <a-select-option value="cancelled">已取消</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
        <a-space>
          <a-button v-if="!isViewMode" type="primary" @click="handleSubmit"
            >保存</a-button
          >
          <a-button @click="handleCancel">{{
            isViewMode ? "返回" : "取消"
          }}</a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <!-- 产品选择器 -->
    <manage-materiel-modelselector
      v-model:visible="productSelectorVisible"
      :multiple="false"
      @selected="handleProductSelected"
    />
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from "vue";
  import { message } from "ant-design-vue";
  import type { Rule } from "ant-design-vue/es/form";
  import dayjs from "dayjs";
  import { UserOutlined } from "@ant-design/icons-vue";
  import UserSelector from "@/components/manage/user/selector.client.vue";
  import { useRouter, useRoute } from "vue-router";

  interface User {
    id: number;
    name: string;
    department?: string;
    email?: string;
  }

  interface TaskFormState {
    id?: number;
    taskNo: string;
    productId?: number;
    productName: string;
    planQuantity?: number;
    planStartTime: dayjs.Dayjs | undefined;
    planEndTime: dayjs.Dayjs | undefined;
    operatorIds: number[];
    selectedOperators: User[];
    remark: string;
    status: string;
    finishCondition: "quantity" | "time";
  }

  const router = useRouter();
  const route = useRoute();
  const formRef = ref();
  const isViewMode = computed(() => route.query.mode === "view");
  const pageTitle = computed(() => {
    if (isViewMode.value) return "查看工单详情";
    return formState.id ? "编辑工单" : "创建工单";
  });

  // 表单状态
  const formState = reactive<TaskFormState>({
    id: undefined,
    taskNo: "",
    productId: undefined,
    productName: "",
    planQuantity: 1,
    planStartTime: dayjs(),
    planEndTime: dayjs().add(1, "day"),
    operatorIds: [],
    selectedOperators: [],
    remark: "",
    status: "draft",
    finishCondition: "time",
  });

  // 表单校验规则
  const rules = {
    taskNo: [
      { required: true, message: "请输入工单编号" } as Rule,
      {
        pattern: /^[A-Za-z0-9-]+$/,
        message: "工单编号只能包含字母、数字和横杠",
      } as Rule,
    ],
    productId: [{ required: true, message: "请选择产品" } as Rule],
    planQuantity: [
      { required: true, message: "请输入计划数量" } as Rule,
      { type: "number", min: 1, message: "计划数量必须大于0" } as Rule,
    ],
    planStartTime: [{ required: true, message: "请选择计划开始时间" } as Rule],
    planEndTime: [
      { required: true, message: "请选择计划结束时间" } as Rule,
      {
        validator: async (rule: Rule, value: any) => {
          if (
            value &&
            formState.planStartTime &&
            value <= formState.planStartTime
          ) {
            return Promise.reject("计划结束时间必须大于开始时间");
          }
          return Promise.resolve();
        },
      } as any,
    ],
    selectedOperators: [
      { required: true, message: "请选择生产员" } as Rule,
      {
        type: "array",
        message: "至少选择一名生产员",
        min: 1,
      } as Rule,
    ],
  };

  // 产品选择器状态
  const productSelectorVisible = ref(false);

  // 生成工单编号
  const generateTaskNo = () => {
    const date = dayjs().format("YYYYMMDD");
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0");
    return `PO${date}${random}`;
  };

  // 显示产品选择器
  const showProductSelector = () => {
    productSelectorVisible.value = true;
  };

  // 处理产品选择
  const handleProductSelected = (selected: any[]) => {
    if (selected && selected.length > 0) {
      const product = selected[0];
      formState.productId = product.id;
      formState.productName = `${product.name}-${product.model}`;
    }
  };

  // 显示选中的生产员姓名
  const selectedOperatorNames = computed(() => {
    if (!formState.selectedOperators?.length) return "";
    return formState.selectedOperators.map((user) => user.name).join(", ");
  });

  // 处理操作员选择
  const handleOperatorSelect = (users: User | User[]) => {
    if (Array.isArray(users)) {
      formState.selectedOperators = [...users];
      formState.operatorIds = users.map((user) => user.id);
    }
  };

  // 初始化表单
  onMounted(() => {
    // 检查是否为编辑模式
    const id = route.query.id;
    if (id) {
      // 加载工单数据
      loadTaskData(Number(id));
    } else {
      // 创建模式
      formState.taskNo = generateTaskNo();
    }
  });

  // 加载工单数据
  const loadTaskData = async (id: number) => {
    try {
      // 使用查询接口获取任务数据
      const result =
        await useApiTrpc().admin.production.queryProductionTask.query({
          includeUsers: true,
          take: 100, // 获取足够多的数据，以确保能找到目标ID
          skip: 0,
        });

      if (result && result.code === 1 && result.data) {
        // 在结果中找到对应ID的任务
        const taskList = result.data.result || [];
        const task = taskList.find((item: any) => item.id === id);

        if (task) {
          // 设置表单数据
          Object.assign(formState, {
            id: task.id,
            taskNo: task.code,
            productId: task.materiel_id,
            productName: task.Materiel
              ? `${task.Materiel.name}-${task.Materiel.model}`
              : "",
            planQuantity: task.quantity,
            planStartTime: task.startAt ? dayjs(task.startAt) : undefined,
            planEndTime: task.endAt ? dayjs(task.endAt) : undefined,
            remark: task.description || "",
            status:
              task.status === 0
                ? "draft"
                : task.status === 1
                ? "pending"
                : task.status === 2
                ? "in_progress"
                : task.status === 3
                ? "completed"
                : task.status === 4
                ? "cancelled"
                : "draft",
            finishCondition: task.finishCondition || "time",
          });

          // 加载工单关联的操作员信息
          if (task.users && task.users.length) {
            formState.selectedOperators = task.users.map((userRel: any) => ({
              id: userRel.user.id,
              name: userRel.user.name,
            }));
            formState.operatorIds = task.users.map(
              (userRel: any) => userRel.user.id
            );
          }
        } else {
          message.error("未找到相关工单");
          router.push("/manage/mes/production/task");
        }
      } else {
        message.error("加载工单数据失败");
        router.push("/manage/mes/production/task");
      }
    } catch (error) {
      console.error("加载工单数据失败:", error);
      message.error("加载工单数据失败");
      router.push("/manage/mes/production/task");
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      const data = {
        code: formState.taskNo,
        materiel_id: formState.productId,
        quantity: formState.planQuantity,
        startAt: formState.planStartTime?.format("YYYY-MM-DD"),
        endAt: formState.planEndTime?.format("YYYY-MM-DD"),
        status:
          formState.status === "draft"
            ? 0
            : formState.status === "pending"
            ? 1
            : formState.status === "in_progress"
            ? 2
            : formState.status === "completed"
            ? 3
            : formState.status === "cancelled"
            ? 4
            : 0,
        description: formState.remark,
        finishCondition: formState.finishCondition,
        users: formState.operatorIds.map((id) => ({ user_id: id })),
      };

      // 添加类型安全检查
      if (!data.materiel_id || !data.quantity || !data.startAt || !data.endAt) {
        message.error("请填写必要的表单信息");
        return;
      }

      if (formState.id) {
        // 编辑模式
        await useApiTrpc().admin.production.updateProductionTask.mutate({
          id: formState.id,
          materiel_id: data.materiel_id,
          quantity: data.quantity,
          startAt: data.startAt,
          endAt: data.endAt,
          status: data.status,
          description: data.description,
          finishCondition: data.finishCondition,
          users: data.users,
        });
        message.success("更新成功");
      } else {
        // 新建模式
        await useApiTrpc().admin.production.createProductionTask.mutate({
          code: data.code,
          materiel_id: data.materiel_id,
          quantity: data.quantity,
          startAt: data.startAt,
          endAt: data.endAt,
          status: data.status,
          description: data.description,
          finishCondition: data.finishCondition,
          users: data.users,
        });
        message.success("创建成功");
      }

      // 返回列表页
      router.push("/manage/mes/production/task");
    } catch (error) {
      console.error("保存工单失败:", error);
      message.error(formState.id ? "更新失败" : "创建失败");
    }
  };

  // 取消编辑
  const handleCancel = () => {
    router.push("/manage/mes/production/task");
  };
</script>

<style scoped>
  .ant-card {
    /* max-width: 800px;
    margin: 20px auto; */
  }
</style>
