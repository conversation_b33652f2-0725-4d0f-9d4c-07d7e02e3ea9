/**
 * This is your entry point to setup the root configuration for tRPC on the server.
 * - `initTRPC` should only be used once per app.
 * - We export only the functionality that we use so we can enforce which base procedures should be used
 *
 * Learn how to create protected base procedures and other things below:
 * @see https://trpc.io/docs/v10/router
 * @see https://trpc.io/docs/v10/procedures
 */
import { initTRPC } from "@trpc/server";
import { Context } from "~/server/trpc/context";
import { z } from "zod";
import { TRPCError } from "@trpc/server";
interface Meta {
  permission: string[];
  authRequired: boolean;
}
const t = initTRPC
  .context<Context>()
  .meta<Meta>()
  .create({
    defaultMeta: {
      authRequired: false,
      permission: [],
    },
  });
/**
 * 公共过程,不可以不直接使用
 **/
const publicProcedure = t.procedure;
/**
 * 日志记录过程,不可以直接使用
 */
const loggedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  const start = Date.now();
  const result = await next({ ctx });
  const duration = Date.now() - start;

  if (process.env.NODE_ENV === "development") {
    result.ok
      ? console.log(
          `${ctx.user?.name}执行了[${ctx.event.method}]${decodeURIComponent(
            ctx.event.path
          )}，耗时${duration}ms}`
        )
      : console.error(
          `${ctx.user?.name}执行了${decodeURIComponent(
            ctx.event.path
          )}，耗时${duration}ms，失败`
        );
  }

  return result;
});
/**
 * 权限验证过程,未规定输出格式,不建议直接使用
 */
const authedProcedure = loggedProcedure.use(async ({ meta, ctx, next }) => {
  // console.log("ctx", ctx.user);
  if (meta && meta.authRequired) {
    const user = ctx.user;
    if (!user) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "未登录" });
    }
    //如果权限中包含*，则表示拥有所有权限,直接放行
    if (user.role.Permission.includes("*")) {
      return next({ ctx });
    }
    if (meta.permission.length > 0) {
      const hasPermission = meta.permission.some((p) => {
        return user.role.Permission.includes(p);
      });
      if (!hasPermission) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "没有权限",
        });
      }
    }
  }

  return next({ ctx });
});
/**
 * 表格结果过程,输出格式为表格结果
 */
export const tableResultProcedure = authedProcedure
  .input(
    z
      .object({
        take: z.number().default(10),
        skip: z.number().default(0),
      })
      .default({ take: 10, skip: 0 })
  )
  .output(
    z.object({
      code: z.number(),
      message: z.string(),
      data: z.object({
        result: z.array(z.any()),
        total: z.number(),
      }),
    })
  );
/**
 * 单个结果过程,输出格式为单个结果
 */
export const singleResultProcedure = authedProcedure.output(
  z.object({
    code: z.number(),
    message: z.string(),
    data: z.any(),
  })
);

export const router = t.router;
export const middleware = t.middleware;
export const mergeRouters = t.mergeRouters;
