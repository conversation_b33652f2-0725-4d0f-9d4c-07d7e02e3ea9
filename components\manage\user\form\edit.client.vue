<template>
  <UiStoremanageForm
    :model="model"
    @submit="submit"
    :rules="rules"
    v-model:loading="loading"
    v-model:visible="visible"
  >
    <template #default="{ show }">
      <a @click="show">编辑</a>
    </template>
    <template #form>
      <a-form-item label="编号" name="code">
        <a-input placeholder="请输入编号" v-model:value="model.code" />
      </a-form-item>
      <a-form-item label="姓名" name="name">
        <a-input placeholder="请输入姓名" v-model:value="model.name" />
      </a-form-item>
      <a-form-item label="用户名" name="username">
        <a-input placeholder="请输入用户名" v-model:value="model.username" />
      </a-form-item>
      <a-form-item label="密码(不修改无需填写)" name="password">
        <a-input-password
          placeholder="请输入密码"
          v-model:value="model.password"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item label="确认密码" name="repassword">
        <a-input-password
          placeholder="请输入确认密码"
          v-model:value="model.repassword"
          autocomplete="new-password"
        />
      </a-form-item>
      <a-form-item label="角色" name="role_id">
        <manage-user-role-selector
          v-model:value="model.Role"
        ></manage-user-role-selector>
      </a-form-item>
      <a-form-item label="邮箱" name="email">
        <a-input placeholder="请输入邮箱" v-model:value="model.email" />
      </a-form-item>
      <a-form-item label="联系电话" name="contactTel">
        <a-input
          placeholder="请输入联系电话"
          v-model:value="model.contactTel"
        />
      </a-form-item>
      <a-form-item label="锁定" name="lock">
        <a-switch v-model:checked="model.lock" />
      </a-form-item>
    </template>
  </UiStoremanageForm>
</template>
<script lang="ts" setup>
  import type { Rule } from "ant-design-vue/es/form";

  type UserForm = API.User.Update & {
    repassword: string;
  };

  const props = defineProps({
    model: {
      type: Object as PropType<UserForm>,
      default: () => {},
    },
  });
  const model = ref<UserForm>(props.model);
  watch(
    () => props.model,
    (val) => {
      model.value = val;
    },
    { deep: true }
  );

  const loading = ref(false);
  const visible = defineModel<boolean>("visible", { default: false });
  const emits = defineEmits(["saveSuccess"]);
  const submit = async () => {
    const {
      code,
      message: msg,
      data,
    } = await useApiFetch.updateUser(model.value);
    if (code) {
      message.success(msg);
      emits("saveSuccess");
      useNuxtApp().$emitter.emit("formSaveSuccessed", data);
      loading.value = false;
    } else {
      message.error(msg);
      loading.value = false;
    }
  };
  const rules: Record<string, Rule[]> = reactive({
    repassword: [
      {
        validator: (rule: Rule, value: string) => {
          if (value !== model.value.password) {
            return Promise.reject("两次密码不一致");
          }
          return Promise.resolve();
        },
        trigger: "blur",
      },
    ],
  });
</script>
