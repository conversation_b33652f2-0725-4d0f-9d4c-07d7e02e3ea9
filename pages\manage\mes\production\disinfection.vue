<template>
  <div class="disinfection-management">
    <a-card title="消毒处理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增处理记录
          </a-button>
        </a-space>
      </template>

      <!-- 使用系统封装的表格组件 -->
      <manage-base-table
        ref="tableRef"
        :columns="columns"
        :model="searchForm"
        :query="queryDisinfectionList"
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="code" label="物料编号">
            <manage-base-search-input
              v-model:value="searchForm.code"
              placeholder="请输入物料编号"
            />
          </a-form-item>
          <a-form-item name="name" label="物料名称">
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入物料名称"
            />
          </a-form-item>
          <a-form-item name="status" label="处理状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择处理状态"
              style="width: 150px"
              allowClear
            >
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="completed">已处理</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 表格单元格自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 新增/编辑消毒处理记录弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :maskClosable="false"
      :closable="true"
      @cancel="handleCancel"
      width="700px"
    >
      <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical">
        <!-- 如果是从库存列表选择的物料，显示物料信息卡片 -->
        <a-card
          v-if="formState.selectedMaterial"
          :bordered="false"
          style="margin-bottom: 16px; background: #f5f5f5"
        >
          <template #title>
            <a-row>
              <a-col :span="18">选中物料信息</a-col>
              <a-col :span="6" style="text-align: right">
                <a-button
                  type="primary"
                  size="small"
                  @click="handleReselectMaterial"
                >
                  <template #icon><ReloadOutlined /></template>
                  重新选择
                </a-button>
              </a-col>
            </a-row>
          </template>
          <a-descriptions
            :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }"
          >
            <a-descriptions-item label="物料编号">
              {{ formState.selectedMaterial.code }}
            </a-descriptions-item>
            <a-descriptions-item label="物料名称">
              {{ formState.selectedMaterial.name }}
            </a-descriptions-item>
            <a-descriptions-item label="规格型号">
              {{ formState.selectedMaterial.specification || "无" }}
            </a-descriptions-item>
            <a-descriptions-item label="单位">
              {{ formState.selectedMaterial.unit }}
            </a-descriptions-item>
            <a-descriptions-item label="批号">
              {{ formState.selectedMaterial.batch_no }}
            </a-descriptions-item>
            <a-descriptions-item label="库存数量">
              {{ formState.selectedMaterial.quantity }}
            </a-descriptions-item>
            <a-descriptions-item label="所在仓库">
              {{ formState.selectedMaterial.warehouse }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 基本信息表单 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="quantity" label="数量">
              <a-input-number
                v-model:value="formState.quantity"
                placeholder="请输入数量"
                style="width: 100%"
                :min="0"
                :precision="0"
                :step="1"
                :parser="(value) => Number(value) || 0"
                :formatter="(value) => `${value}`"
                :max="
                  formState.selectedMaterial
                    ? Number(formState.selectedMaterial.quantity) || 0
                    : undefined
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="batch_no" label="批号">
              <a-input
                v-model:value="formState.batch_no"
                placeholder="请输入批号"
                :disabled="!!formState.selectedMaterial"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item name="status" label="处理状态">
              <a-select
                v-model:value="formState.status"
                placeholder="请选择处理状态"
                style="width: 100%"
              >
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已处理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item name="remark" label="备注">
          <a-textarea
            v-model:value="formState.remark"
            placeholder="请输入备注"
            :rows="4"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="saveLoading">
          保存
        </a-button>
      </template>
    </a-modal>

    <!-- 库存列表弹窗 -->
    <a-modal
      v-model:open="inventoryModalVisible"
      title="生产库存列表"
      :maskClosable="false"
      :closable="true"
      width="80%"
      @cancel="handleInventoryCancel"
    >
      <manage-base-table
        ref="inventoryTableRef"
        :columns="inventoryColumns"
        :model="inventorySearchForm"
        :query="queryInventoryList"
        rowKey="id"
        bordered
      >
        <template #searchBox>
          <a-form-item name="materialCode" label="物料编号">
            <manage-base-search-input
              v-model:value="inventorySearchForm.materialCode"
              placeholder="请输入物料编号"
            />
          </a-form-item>
          <a-form-item name="materialName" label="物料名称">
            <a-input
              v-model:value="inventorySearchForm.materialName"
              placeholder="请输入物料名称"
            />
          </a-form-item>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="primary" @click="handleProcess(record)"
              >处理</a-button
            >
          </template>
        </template>
      </manage-base-table>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from "vue";
  import { PlusOutlined, ReloadOutlined } from "@ant-design/icons-vue";
  import { message, Modal } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue/es/form";
  import type { TableInstance } from "~/components/manage/base/table.client.vue";
  import { createDisinfectionRecordSchema } from "@/schemas/production";
  import { z } from "zod";

  // 消毒处理记录类型
  interface DisinfectionRecord {
    id: number;
    code: string;
    name: string;
    quantity: number;
    unit: string;
    status: string;
    remark: string;
    batch_no: string;
    warehouse_id: number;
  }

  // 表格列定义
  const columns = [
    {
      title: "物料编号",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 150,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 100,
    },
    {
      title: "处理数量",
      dataIndex: "quantity",
      width: 100,
    },
    {
      title: "单位",
      dataIndex: ["materiel", "unit"],
      width: 80,
    },
    {
      title: "备注",
      dataIndex: "note",
      width: 200,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      width: 150,
      customRender: ({ text }: { text: string }) => {
        return text ? new Date(text).toLocaleString() : "-";
      },
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      width: 150,
      customRender: ({ text }: { text: string }) => {
        return text ? new Date(text).toLocaleString() : "-";
      },
    },
  ];

  // 搜索表单
  const searchForm = reactive({
    code: "",
    name: "",
    status: undefined,
  });

  // 表格实例
  const tableRef = ref<TableInstance>();

  // 表单实例
  const formRef = ref<FormInstance>();

  // 弹窗控制
  const modalVisible = ref(false);
  const modalTitle = ref("新增消毒处理记录");
  const saveLoading = ref(false);

  // 表单状态
  const formState = reactive({
    id: undefined,
    code: "",
    materialId: undefined,
    name: "",
    quantity: 0,
    unit: "",
    status: "pending",
    remark: "",
    batch_no: "",
    warehouse_id: undefined as number | undefined, // 仓库ID
    // 选中的物料信息，用于在表单中显示
    selectedMaterial: null as any,
  });

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: "warning",
      processing: "processing",
      completed: "success",
    };
    return colorMap[status] || "default";
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: "待处理",
      processing: "处理中",
      completed: "已处理",
    };
    return textMap[status] || status;
  };

  // 查询消毒处理记录列表
  const queryDisinfectionList =
    useApiTrpc().admin.production.queryDisinfectionRecord.query;

  // 新增按钮点击
  const handleAdd = () => {
    // 重置表单状态
    Object.assign(formState, {
      id: undefined,
      code: "",
      materialId: undefined,
      name: "",
      quantity: 0,
      unit: "",
      status: "pending",
      remark: "",
      batch_no: "",
      warehouse_id: undefined,
      selectedMaterial: null,
    });

    // 打开库存列表弹窗
    handleOpenInventory();
  };

  // 取消按钮点击
  const handleCancel = () => {
    modalVisible.value = false;
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 验证表单
      await formRef.value?.validate();

      // 构建提交数据
      const submitData = {
        id: formState.id,
        code: formState.code,
        materialId: formState.materialId as number,
        name: formState.name,
        quantity: Number(formState.quantity) || 0, // 确保是数字类型
        unit: formState.unit,
        status: formState.status,
        remark: formState.remark,
        batch_no: formState.batch_no,
        // 获取生产库 ID
        warehouse_id: inventorySearchForm.warehouseId,
      };

      // 打印提交数据，便于调试
      console.log("提交数据", submitData);
      console.log("数量类型", typeof submitData.quantity, submitData.quantity);

      saveLoading.value = true;

      try {
        if (submitData.id) {
          // 更新消毒处理记录
          // 实际应用中应该调用后端 API
          // await useApiTrpc().admin.production.updateDisinfectionRecord.mutate(submitData);

          // 模拟成功响应
          console.log("更新消毒处理记录", submitData);
          message.success("更新成功");
        } else {
          // 创建消毒处理记录
          await useApiTrpc().admin.production.createDisinfectionRecord.mutate({
            materiel_id: submitData.materialId,
            batch_no: submitData.batch_no,
            quantity: Number(submitData.quantity) || 0, // 确保是数字类型
            warehouse_id: submitData.warehouse_id,
            remark: submitData.remark,
          });

          console.log("创建消毒处理记录", submitData);
          message.success("创建成功");
        }

        // 关闭弹窗并刷新列表
        modalVisible.value = false;
        tableRef.value?.query();
      } catch (apiError: any) {
        console.error("API 调用失败", apiError);
        message.error(`提交失败：${apiError.message || "未知错误"}`);
      }
    } catch (error) {
      // 表单验证错误
      console.error("表单验证错误", error);
      message.error("表单验证失败，请检查输入");
    } finally {
      saveLoading.value = false;
    }
  };

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: "请输入物料名称" }],
    quantity: [
      {
        required: true,
        type: "number" as const,
        min: 0,
        message: "数量不能小于0",
      },
    ],
    unit: [{ required: true, message: "请输入单位" }],
    status: [{ required: true, message: "请选择处理状态" }],
  };

  // 库存列表相关
  const inventoryModalVisible = ref(false);
  const inventoryTableRef = ref<TableInstance>();
  const inventorySearchForm = reactive({
    materialCode: "",
    materialName: "",
    warehouseId: undefined as number | undefined,
  });

  // 获取生产库信息
  const fetchProductionWarehouse = async () => {
    try {
      const result =
        await useApiTrpc().admin.warehouse.queryWarehouseList.query({
          type: ["finished"],
        });
      if (result?.data?.result?.length > 0) {
        // 如果找到生产库，将其 ID 设置到搜索表单中
        const productionWarehouse = result.data.result[0];
        inventorySearchForm.warehouseId = productionWarehouse.id;
      }
    } catch (error) {
      console.error("获取生产库信息失败", error);
    }
  };

  // 库存列表列定义
  const inventoryColumns = [
    {
      title: "物料编号",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 150,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 150,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 100,
    },
    {
      title: "单位",
      dataIndex: ["materiel", "unit"],
      width: 80,
    },
    {
      title: "库存数量",
      dataIndex: "quantity",
      width: 100,
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
    {
      title: "操作",
      key: "action",
      width: 100,
      fixed: "right" as const,
    },
  ];

  // 查询库存列表
  const queryInventoryList = useApiTrpc().admin.stock.queryStock.query;

  // 打开库存列表弹窗
  const handleOpenInventory = async () => {
    // 获取生产库信息
    await fetchProductionWarehouse();

    // 打开弹窗
    inventoryModalVisible.value = true;

    // 刷新库存列表
    setTimeout(() => {
      inventoryTableRef.value?.query();
    }, 100);
  };

  // 关闭库存列表弹窗
  const handleInventoryCancel = () => {
    inventoryModalVisible.value = false;
  };

  // 处理按钮点击
  const handleProcess = (record: any) => {
    // 填充表单数据
    if (record.materiel) {
      // 打印记录信息，便于调试
      console.log("选中的物料记录", record);

      // 保存选中的物料信息，便于在表单中显示
      const selectedMaterial = {
        id: record.materiel.id,
        code: record.materiel.code,
        name: record.materiel.name,
        specification: record.materiel.specification,
        batch_no: record.batch_no || "", // 确保有默认值
        unit: record.materiel.unit,
        warehouse: record.warehouse?.name || "仓库",
        warehouse_id: record.warehouse?.id || inventorySearchForm.warehouseId, // 仓库ID
        quantity: Number(record.quantity) || 0, // 确保是数字类型
      };

      // 重置表单状态
      Object.assign(formState, {
        id: undefined,
        code: `D${new Date().getTime().toString().slice(-6)}`, // 生成消毒编号
        materialId: selectedMaterial.id,
        name: selectedMaterial.name,
        quantity: Number(selectedMaterial.quantity) || 0, // 确保是数字类型
        unit: selectedMaterial.unit,
        status: "pending",
        remark: `来源于${selectedMaterial.warehouse}的物料，规格：${
          selectedMaterial.specification || "无"
        }`,
        batch_no: selectedMaterial.batch_no, // 使用选中物料中的批号
        warehouse_id: selectedMaterial.warehouse_id, // 仓库ID
        // 保存原始物料信息，便于在表单中显示
        selectedMaterial: selectedMaterial,
      });

      // 打印表单状态，便于调试
      console.log("表单状态", formState);

      // 设置模态框标题
      modalTitle.value = "新增消毒处理记录";

      // 关闭库存列表弹窗
      inventoryModalVisible.value = false;
      // 打开处理记录弹窗
      modalVisible.value = true;

      // 提示用户
      message.success(`已选择物料：${selectedMaterial.name}`);
    } else {
      message.error("物料信息不完整");
    }
  };

  // 重新选择物料按钮点击
  const handleReselectMaterial = () => {
    // 清除当前选中的物料信息
    formState.selectedMaterial = null;

    // 关闭当前弹窗
    modalVisible.value = false;

    // 打开库存列表弹窗
    handleOpenInventory();

    // 提示用户
    message.info("请重新选择要处理的物料");
  };

  // 在组件挂载时初始化
  onMounted(() => {
    // 初始化消毒处理列表
    tableRef.value?.query();
  });
</script>

<style scoped>
  .disinfection-management {
    padding: 0;
  }
</style>
