import { H3Event } from "h3";
export const checkPermission = (event: H3Event, authRoles: string[] = []) => {
  const user = event.context.auth;
  if (user == null || user == undefined) {
    throw createError({
      message: "未登录",
      status: 401,
      statusCode: 401,
    });
  } else {
    if (authRoles.length > 0) {
      if (!authRoles.includes(user.Role)) {
        throw createError({
          message: "无权限",
          status: 403,
          statusCode: 403,
        });
      }
    }
  }
  return user;
};
