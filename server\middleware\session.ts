import { H3Event } from 'h3';
import { v4 as uuidv4 } from 'uuid';

// 声明session存储类型
declare module 'h3' {
  interface H3EventContext {
    session: {
      id: string;
      createdAt: Date;
      data: Record<string, any>;
    };
  }
}

// 内存中的session存储
const sessions: Record<string, {
  id: string;
  createdAt: Date;
  data: Record<string, any>;
  lastAccessed: Date;
}> = {};

// 定期清理过期的session
const SESSION_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24小时
const CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 每小时清理一次

// 清理过期session的函数
function cleanupSessions() {
  const now = Date.now();
  Object.keys(sessions).forEach(sessionId => {
    const session = sessions[sessionId];
    if (now - session.lastAccessed.getTime() > SESSION_EXPIRY_MS) {
      delete sessions[sessionId];
    }
  });
}

// 设置定期清理
if (process.server) {
  setInterval(cleanupSessions, CLEANUP_INTERVAL_MS);
}

// session中间件
export default defineEventHandler((event: H3Event) => {
  // 从cookie中获取sessionId
  let sessionId = getCookie(event, 'sessionId');
  
  // 如果没有sessionId或者sessionId不存在于sessions中，创建新的session
  if (!sessionId || !sessions[sessionId]) {
    sessionId = uuidv4();
    sessions[sessionId] = {
      id: sessionId,
      createdAt: new Date(),
      data: {},
      lastAccessed: new Date()
    };
    
    // 设置cookie
    setCookie(event, 'sessionId', sessionId, {
      httpOnly: true,
      path: '/',
      maxAge: SESSION_EXPIRY_MS / 1000, // 转换为秒
      sameSite: 'lax'
    });
  } else {
    // 更新最后访问时间
    sessions[sessionId].lastAccessed = new Date();
  }
  
  // 将session添加到event.context中
  event.context.session = {
    id: sessionId,
    createdAt: sessions[sessionId].createdAt,
    data: sessions[sessionId].data
  };
});
