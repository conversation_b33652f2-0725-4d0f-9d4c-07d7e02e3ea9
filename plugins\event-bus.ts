// plugins/event-bus.ts
import mitt from "mitt";

// 为 mitt 方法里的’modify-user‘事件标注类型
interface ModifyUser {
  id: number;
  name: string;
}

// 为 mitt 方法标注类型
type ApplicationEvents = {
  // 'delete-user': number
  // 'modify-user': ModifyUser
  changeLoginBoxStatus: boolean;
  loginSuccessed: any;
  loginFailed: any;
  formSaveSuccessed: any;
  error: 0 | 400 | 401 | 403 | 404 | 500 | null;
};

export default defineNuxtPlugin(() => {
  const emitter = mitt<ApplicationEvents>();

  return {
    provide: {
      emitter,
      // emits: emitter.emit, // 触发事件方法 $emits
      // on: emitter.on, // 监听事件方法 $on
    },
  };
});
