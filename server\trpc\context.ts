import { inferAsyncReturnType } from "@trpc/server";
import type { H3Event } from "h3";
import { prisma } from "@/lib/prisma";
import { useJwt } from "@/server/utils/jwt";
import { useSession } from "@/server/utils/session";

/**
 * Creates context for an incoming request
 * @link https://trpc.io/docs/context
 */
export const createContext = (event: H3Event) => {
  // 检查用户是否已登录
  function checkLogin(event: H3Event) {
    const token = getCookie(event, "jwt");
    if (token) {
      return useJwt().verify(token);
    } else {
      return undefined;
    }
  }

  // 获取用户信息
  const user = checkLogin(event);

  // 获取session
  const session = event.context.session;

  // 如果用户已登录，更新session中的用户活动时间
  if (user) {
    const sessionUtils = useSession(event);
    sessionUtils.updateUserActivity();
  }

  return {
    event,
    user,
    session,
    prisma,
  };
};

export type Context = inferAsyncReturnType<typeof createContext>;
