<template>
  <a-layout :style="pageStyle">
    <a-layout-header :style="headerStyle">
      <slot name="header"></slot>
    </a-layout-header>
    <a-layout-content :style="contentStyle">
      <a-space align="center" style="margin-top: 10%">
        <slot></slot>
      </a-space>
    </a-layout-content>
    <a-layout-footer :style="footerStyle">
      电 话：010-82295935 82295957 地址：北京市东城区东安门大街55号王府世纪620
    </a-layout-footer>
  </a-layout>
</template>
<script lang="ts" setup>
  import type { CSSProperties } from "vue";
  const pageStyle: CSSProperties = {
    width: "100%",
    height: "100%",
  };
  const headerStyle: CSSProperties = {
    textAlign: "center",
    color: "#fff",
    height: 64,
    paddingInline: 50,
    lineHeight: "64px",
    backgroundColor: "rgb(32, 125, 195)",
  };

  const contentStyle: CSSProperties = {
    textAlign: "center",
    minHeight: 120,
    lineHeight: "120px",
    color: "#fff",
    backgroundColor: "rgb(115, 163, 212)",
  };

  const footerStyle: CSSProperties = {
    textAlign: "center",
    color: "#fff",
    backgroundColor: "rgb(24, 93, 135)",
  };
</script>
<style scoped></style>
