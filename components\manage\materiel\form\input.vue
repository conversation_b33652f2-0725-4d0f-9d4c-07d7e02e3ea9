<template>
  <div>
    <a-input-group compact>
      <a-input
        v-model:value="showText"
        :placeholder="placeholder"
        readonly
        style="width: calc(100% - 65px)"
        @click="showProductSelector"
      />
      <a-button type="primary" @click="showProductSelector" :disabled="disabled"
        >选择</a-button
      >
    </a-input-group>
    <!-- 产品选择器 -->
    <manage-materiel-modelselector
      v-model:visible="productSelectorVisible"
      :multiple="false"
      @selected="handleProductSelected"
    />
  </div>
</template>
<script setup lang="ts">
  const props = defineProps({
    placeholder: {
      type: String,
      default: "请选择物料",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const value = defineModel<number>("value");
  const productSelectorVisible = ref(false);
  const name = ref("");
  const model = ref("");
  const showText = computed(() => {
    if (!name.value || !model.value) {
      return undefined;
    }
    return `${name.value}-${model.value}`;
  });

  const showProductSelector = () => {
    productSelectorVisible.value = true;
  };
  // 处理产品选择
  const handleProductSelected = (selected: any[]) => {
    if (selected && selected.length > 0) {
      const product = selected[0];
      value.value = product.id;
      name.value = product.name;
      model.value = product.model;
    }
  };
</script>
