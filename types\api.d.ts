type PromiseReturnType<T extends (...args: any) => any> = T extends (
  ...args: any
) => Promise<infer PR>
  ? PR
  : ReturnType<T>;

type DeepPartial<T> = T extends Function
  ? T
  : T extends object
  ? { [P in keyof T]?: Exclude<DeepPartial<T[P]>, null> }
  : T;

type DeepExcludeSthtype<T, E> = T extends object
  ? { [P in keyof T]: DeepExcludeSthtype<T[P], E> }
  : T extends E
  ? never
  : T;
type PartialSth<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
