// 全屏功能封装
export const useFullScreen = () => {
  if (import.meta.client) {
    return {
      enter() {
        const el = document.documentElement;
        const methods = [
          "requestFullscreen",
          "webkitRequestFullscreen",
          "msRequestFullscreen",
        ] as const;

        for (const method of methods) {
          const fn = el[method] as unknown as (() => Promise<void>) | undefined;
          if (fn) {
            fn.call(el).catch((err: Error) => {
              console.error(`全屏请求失败: ${err}`);
            });
            break;
          }
        }
      },

      exit() {
        const methods = [
          "exitFullscreen",
          "webkitExitFullscreen",
          "msExitFullscreen",
        ] as const;

        for (const method of methods) {
          const fn = document[method] as unknown as (() => void) | undefined;
          if (fn) {
            fn.call(document);
            break;
          }
        }
      },

      toggle() {
        if (!this.isFullscreen()) {
          this.enter();
        } else {
          this.exit();
        }
      },

      isFullscreen() {
        return !!(
          document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.msFullscreenElement
        );
      },
      autoFullscreen() {
        if (!this.isFullscreen()) {
          this.enter();
        }
      },
    };
  }
  return undefined;
};
