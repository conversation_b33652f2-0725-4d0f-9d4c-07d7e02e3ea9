import { H3Event } from "h3";

/**
 * 使用自定义session
 * @param event H3Event对象
 * @returns session操作方法
 */
export function useSession(event: H3Event) {
  /**
   * 获取session中的值
   * @param key 键名
   * @returns 对应的值
   */
  function get(key: string): any {
    return event.context.session.data[key];
  }

  /**
   * 设置session值
   * @param key 键名
   * @param value 值
   */
  function set(key: string, value: any): void {
    event.context.session.data[key] = value;
  }

  /**
   * 删除session中的键
   * @param key 要删除的键名
   */
  function unset(key: string): void {
    delete event.context.session.data[key];
  }

  /**
   * 清空session数据
   */
  function clear(): void {
    event.context.session.data = {};
  }

  /**
   * 设置验证码
   * @param text 验证码文本
   * @param expiryInSeconds 过期时间(秒)，默认5分钟
   */
  function setCaptcha(text: string, expiryInSeconds: number = 300): void {
    const expireAt = new Date();
    expireAt.setSeconds(expireAt.getSeconds() + expiryInSeconds);

    set("captcha", {
      text,
      expireAt,
    });
  }

  /**
   * 验证验证码
   * @param inputText 用户输入的验证码
   * @returns 验证结果，true为验证通过
   */
  function verifyCaptcha(inputText: string): boolean {
    const captcha = get("captcha");

    if (!captcha) return false;
    if (new Date() > new Date(captcha.expireAt)) return false;

    return inputText.toLowerCase() === captcha.text.toLowerCase();
  }

  /**
   * 设置用户会话信息
   * @param userData 用户数据
   */
  function setUser(userData: { id: number; username: string }): void {
    set("user", {
      id: userData.id,
      username: userData.username,
      lastActive: new Date(),
    });
  }

  /**
   * 更新用户最后活动时间
   */
  function updateUserActivity(): void {
    const user = get("user");
    if (user) {
      user.lastActive = new Date();
      set("user", user);
    }
  }

  return {
    get,
    set,
    unset,
    clear,
    setCaptcha,
    verifyCaptcha,
    setUser,
    updateUserActivity,
  };
}

// 为了向后兼容，保留原来的函数名
export function useSideBaseSession(event: H3Event) {
  return useSession(event);
}
