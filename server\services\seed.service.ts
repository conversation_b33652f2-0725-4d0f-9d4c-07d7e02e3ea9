import { prisma, Prisma } from "~/lib/prisma";
import { getItems } from "~/utils/manage-menu";
import { RouteScanner } from "~/utils/route-scanner";

import type { MenuItem } from "~/components/manage/navigation/index.vue";
import { menuItems } from "~/utils/manage-menu";
const createMenu = async (
  tx: Prisma.TransactionClient,
  item: MenuItem,
  parentId?: number
) => {
  const menu = await tx.menu.create({
    data: {
      name: item.name,
      title: item.title,
      icon: item.icon?.toString(),
      sort: item.sort,
      parentId,
      type: item.type,
    },
  });
  if (item.items) {
    for (const child of item.items) {
      await createMenu(tx, child, menu.id);
    }
  }
};

export class SeedService {
  static async syncAll() {
    try {
      await this.syncRole();
      await this.syncUsers();
      await this.syncSystem();
      if (process.env.NODE_ENV === "development") {
        await this.syncExampleData();
      }
      console.log("所有数据同步完成");
    } catch (error) {
      console.error("数据同步失败:", error);
      throw error;
    }
  }
  static async syncMenu() {
    const system = await prisma.system.findFirst();
    if (system) {
      const menuCount = system.menuCount;
    }
  }
  static async syncRole() {
    // const permission = await prisma.permission.findMany();
    // const permissionIds = permission.map((item) => item.id);
    const role = await prisma.role.createMany({
      data: [
        {
          name: "超级管理员",
          code: "superadmin",
          Permission: ["*"],
          Menu: [
            "home",
            "system",
            "system/user",
            "system/warehouse",
            "system/customer",
            "system/auth",
            "system/role",
            "materiel",
            "materiel/materiel",
            "purchase",
            "purchase/supplier",
            "purchase/order",
            "production",
            "production/plan",
            "production/task",
            "production/reporting",
            "production/schedule",
            "production/performance",
            "production/statistics",
            "production/waste",
            "stock",
            "stock/stock",
            "stock/purchase",
            "stock/return",
            "stock/productionin",
            "stock/productionout",
            "stock/sale",
          ],
        },
        {
          name: "生产管理员",
          code: "productionadmin",
          Permission: [],
          Menu: [],
        },
        {
          name: "生产员",
          code: "productionuser",
          Permission: ["*"],
          Menu: ["production", "production/reporting"],
        },
      ],
    });
  }
  static async syncUsers() {
    const admin: Prisma.UserCreateInput = {
      username: "uargly_admin",
      password: "Uarlabadmin_240930",
      code: "uarlab001",
      name: "超级管理员",
      email: "<EMAIL>",
      role: {
        connect: {
          code: "superadmin",
        },
      },
    };
    const productionAdmin: Prisma.UserCreateInput = {
      username: "uarsc_admin",
      password: "uarlab",
      code: "uarlab002",
      name: "生产管理员",
      email: "<EMAIL>",
      role: {
        connect: {
          code: "productionadmin",
        },
      },
    };
    const productionUser1: Prisma.UserCreateInput = {
      username: "uarsc_user",
      password: "uarlab",
      code: "uarlab003",
      name: "生产员1",
      email: "<EMAIL>",
      role: {
        connect: {
          code: "productionuser",
        },
      },
    };
    const productionUser2: Prisma.UserCreateInput = {
      username: "uarsc_user2",
      password: "uarlab",
      code: "uarlab004",
      name: "生产员2",
      email: "<EMAIL>",
      role: {
        connect: {
          code: "productionuser",
        },
      },
    };
    await prisma.user.create({
      data: admin,
    });
    await prisma.user.create({
      data: productionAdmin,
    });
    await prisma.user.create({
      data: productionUser1,
    });
    await prisma.user.create({
      data: productionUser2,
    });
  }

  static async syncSystem() {
    const menuCount = await prisma.menu.count();
    const permissionCount = await prisma.permission.count();
    await prisma.system.upsert({
      where: { id: 1 },
      create: { installed: true, menuCount, permissionCount },
      update: { installed: true, menuCount, permissionCount },
    });
  }

  static async syncExampleData() {
    // 示例仓库数据
    const warehouseData: Prisma.WarehouseCreateInput[] = [
      {
        name: "测试仓库",
        address: "-",
        WarehouseOnUser: {
          create: {
            user: {
              connect: { id: 1 },
            },
          },
        },
      },
    ];

    // 物料示例数据
    const materielData: Prisma.MaterielCreateInput[] = [
      {
        category: "01",
        attribute: "01",
        type: "00",
        code: "M0001",
        name: "目标产物A",
        model: "A1006",
        description: "测试用商品",
        specification: "台",
        unit: "台",
        Admin: {
          connect: { id: 1 },
        },
      },
    ];

    // 同步示例数据
    for (const wh of warehouseData) {
      await prisma.warehouse.create({ data: wh });
    }

    for (const m of materielData) {
      await prisma.materiel.create({ data: m });
    }
  }
}
