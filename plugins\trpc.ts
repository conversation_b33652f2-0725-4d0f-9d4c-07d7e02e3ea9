import type { TRPCLink } from "@trpc/client";
import { observable } from "@trpc/server/observable";
import { loggerLink } from "@trpc/client/links/loggerLink";
import {
  createTRPCNuxtClient,
  httpBatchLink,
  httpLink,
} from "trpc-nuxt/client";
import type { AppRouter } from "~/server/trpc/routers";
import axios from "axios";

export default defineNuxtPlugin(() => {
  /**
   * createTRPCNuxtClient adds a `useQuery` composable
   * built on top of `useAsyncData`.
   */
  const apiUrl = useRuntimeConfig().public.apiUrl as string;
  const axiosFetch: typeof fetch = async (input, init) => {
    try {
      const axiosInstance = axios.create({
        baseURL: apiUrl,
      });

      const response = await axiosInstance.request({
        url: input.toString(),
        method: init?.method,
        headers: init?.headers as any,
        data: init?.body,
      });

      return new Response(JSON.stringify(response.data));
    } catch (error) {
      console.log(input);
      console.log(error);
      return new Response(JSON.stringify({ error: "请求失败" }));
    }
  };
  const client = createTRPCNuxtClient<AppRouter>({
    links: [
      loggerLink({
        enabled: () => process.env.NODE_ENV === "development",
      }),
      httpBatchLink({
        url: "/api/trpc",
        fetch: axiosFetch,
      }),
    ],
  });
  return {
    provide: {
      client,
    },
  };
});
