<template>
  <a-cascader v-model:value="value" :options="options" :load-data="loadData" placeholder="Please select"
    style="width: 100%" @change="handleChange">
  </a-cascader>
</template>
<script setup lang="ts">
interface Option {
  value?: string | number | null | undefined;
  label?: string;
  loading?: boolean;
  isLeaf?: boolean;
  children?: Option[];
  code?: string;
}
const options = ref<Option[]>([]);
const query = async (param?: API.ProductTypeQueryParams) => {
  const { data } = await useApiFetch.queryMaterielType(param);
  return data.result;
};
onMounted(async () => {
  const data = await query();
  data.forEach((item) => {
    options.value.push({
      value: item.id,
      label: `${item.name}(${item.code})`,
      isLeaf: false,
      loading: false,
      code: item.code,
    });
  });
});
const loadData = (selectedOptions: Option[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  if (
    targetOption.children == undefined ||
    targetOption.children.length == 0
  ) {
    setTimeout(async () => {
      const data = await query({ id: targetOption.value as number });
      if (data.length > 0) {
        targetOption.children = [];
        data.forEach((item) => {
          let isLeaf = false;
          if (item.code.length == 6) {
            isLeaf = true;
          }
          //@ts-ignore
          targetOption.children.push({
            value: item.id,
            label: `${item.name}(${item.code})`,
            isLeaf,
            loading: false,
            code: item.code,
          });
          targetOption.loading = false;
        });
      } else {
        targetOption.isLeaf = true;
      }
    }, 200);
  }
};
const value = ref<number[]>([]);
const type_id = defineModel<number>("typeid", {
  required: true,
});
const code = defineModel<string>("code");
const handleChange = (value: any, selectedOptions: Option[]) => {
  console.log("change", value, selectedOptions);
  type_id.value = value.findLast((item: any) => item != undefined) as number;
  code.value = selectedOptions.findLast((item) => item != undefined)
    ?.code as string;
};
// watch(
//   () => value.value,
//   (val) => {
//     type_id.value = val.findLast((item) => item != undefined) as number;
//   }
// );
</script>
