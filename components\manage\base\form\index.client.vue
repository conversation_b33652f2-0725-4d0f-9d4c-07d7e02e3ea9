<template>
  <a-form v-bind="filteredAttrs">
    <slot></slot>
  </a-form>
</template>

<script setup lang="ts">
  import { Form } from "ant-design-vue";
  import { z } from "zod";
  const useForm = Form.useForm;

  const props = defineProps<{
    zodSchema: z.ZodObject<any>;
  }>();
  const attrs = useAttrs();
  const filteredAttrs = computed(() => {
    return {
      ...attrs,
    };
  });
</script>
