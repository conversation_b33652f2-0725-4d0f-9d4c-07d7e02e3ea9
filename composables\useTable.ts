import type {
  PaginationProps,
  TableProps,
  TableColumnProps,
} from "ant-design-vue";
import type { UnwrapRef } from "vue";

type MyTableOptions<T> = {
  columns: TableColumnProps<T>[];
};
export type MyTableProps<T> = MyTableOptions<T> & {
  model: T;
};
export const useTable = <T>(props: MyTableOptions<T>) => {
  const model: UnwrapRef<T|any> = reactive<{
    [key: string]: any;
  }>({});

  const columns = props.columns || [];
  if (columns) {
    columns.forEach((item) => {
      if (item.dataIndex) {
        const key = item.dataIndex.toString();
        model[key] = undefined;
      }
    });
  }
  return reactive({
    columns,
    model,
  } as MyTableProps<typeof model>);
};
