<template>
  <!-- <div>test</div> -->
  <NuxtLayout>
    <template #header>
      <a-space>
        <a-button @click="fullscreen()">
          <FullscreenOutlined />全屏显示
        </a-button>
        <a-button @click="goHome"> <HomeFilled />欢迎,{{ name }} </a-button>
        <a-button @click="updatePwd()"> <EditFilled />修改密码 </a-button>
        <a-button @click="logout()"> <LogoutOutlined />退出登录 </a-button>
      </a-space>
    </template>
    <template #menu>
      <manage-navigation @selectMenuItem="handleClick" :items="menuItems">
      </manage-navigation>
    </template>
    <NuxtPage />
  </NuxtLayout>
</template>
<script setup lang="ts">
  definePageMeta({
    // middleware: ["auth"],
    layout: "manage-frame",
  });
  import {
    HomeFilled,
    HomeOutlined,
    EditFilled,
    LogoutOutlined,
    MailOutlined,
  } from "@ant-design/icons-vue";
  import type { MenuProps, ItemType } from "ant-design-vue";
  import type { MenuInfo } from "ant-design-vue/es/menu/src/interface";
  import type { VueElement } from "vue";
  import type { MenuItem } from "~/components/manage/navigation/index.vue";
  import { useFullScreen } from "~/utils/fullScreen";
  import { getItems } from "~/utils/manage-menu";

  // 扩展 Document 接口
  declare global {
    interface Document {
      webkitFullscreenElement?: Element | null;
      msFullscreenElement?: Element | null;
      webkitExitFullscreen?: () => void;
      msExitFullscreen?: () => void;
    }
    interface HTMLElement {
      webkitRequestFullscreen?: () => Promise<void>;
      msRequestFullscreen?: () => Promise<void>;
    }
  }

  const emits = defineEmits<{
    goHome: [];
    logout: [];
    updatePwd: [];
    selectMenuItem: [MenuInfo];
  }>();
  // const menus = ["home"];
  const result = await useApiTrpc().admin.auth.queryUserMenu.query();
  const menus = result.data.Menu;
  const user = await useAuth().getUser();

  const menuItems = getItems(menus);

  const selectedKeys = defineModel<string[]>("selectedKeys", {
    default: () => ["1"],
  });
  const breadcrumb = defineModel<string[]>("breadcrumb", {
    default: () => [],
  });
  //   const openKeys = ref<string[]>(["sub1"]);

  const handleClick: MenuProps["onClick"] = (e) => {
    navigateTo(`/manage/mes/${e.key}`);
  };

  // const user = await useApi().get()("/api/user/loginstatus");

  const name = ref("");
  if (user) {
    name.value = user.name;
  }
  const activeIndex = ref("logo");
  const handleSelect = (key: any, keyPath: any) => {};

  const goHome = () => {
    emits("goHome");
  };
  const updatePwd = () => {
    emits("updatePwd");
  };
  const logout = async () => {
    // emits("logout");
    Modal.confirm({
      title: "提示",
      content: "确定要退出登录吗？",
      onOk: async () => {
        const exit = await useApiTrpc().public.auth.logout.query();
        if (exit.code) {
          navigateTo("/manage");
        }
      },
    });
  };
  const fullscreen = () => {
    import("~/utils/fullScreen").then((fullscreen) => {
      useFullScreen()?.toggle();
    });
  };
</script>
<style scoped>
  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .el-menu {
    --el-menu-bg-color: #207dc3;
    --el-menu-text-color: #fff;
    /* --el-menu-active-color: #fff; */
  }

  .el-menu--horizontal > .el-menu-item:nth-child(1) {
    margin-right: auto;
  }
</style>
