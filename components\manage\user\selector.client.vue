<template>
  <div>
    <!-- 触发按钮 -->
    <slot name="trigger" :showModal="showModal">
      <a-button @click="showModal">选择人员</a-button>
    </slot>

    <!-- 选择器模态框 -->
    <a-modal
      v-model:open="isOpen"
      title="选择人员"
      width="800px"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <a-input-search
        v-model:value="searchQuery"
        placeholder="搜索人员..."
        style="width: 300px"
        @search="onSearch"
      />

      <!-- 用户列表 -->
      <a-divider />
      <manage-base-table
        :query="query"
        :columns="columns"
        :rowKey="(record: User) => record.id"
        :row-selection="rowSelection"
        :pagination="{ pageSize: 10 }"
        @row-click="onRowClick"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import type { TableRowSelection } from "ant-design-vue/es/table/interface";

  interface User {
    id: number;
    name: string;
    department?: string;
    email?: string;
  }
  const value = defineModel<User | User[]>("value");
  const props = defineProps<{
    multiple?: boolean;
  }>();

  const emit = defineEmits<{
    selected: [value: User | User[]];
  }>();

  // 状态管理
  const isOpen = ref(false);
  const searchQuery = ref("");
  const loading = ref(false);
  const selectedUsers = ref<User[]>([]);

  // 表格列配置
  const columns = [
    { title: "编号", dataIndex: "code" },
    { title: "姓名", dataIndex: "name" },
    { title: "用户名", dataIndex: "username" },
  ];

  // 行选择配置
  const rowSelection = computed<TableRowSelection>(() => ({
    type: props.multiple ? "checkbox" : "radio",
    selectedRowKeys: selectedUsers.value.map((user) => user.id),
    onChange: (selectedRowKeys: (string | number)[], selectedRows: User[]) => {
      selectedUsers.value = selectedRows;
    },
    onSelect: (record: User, selected: boolean) => {
      if (!props.multiple) {
        selectedUsers.value = selected ? [record] : [];
      } else {
        if (selected) {
          selectedUsers.value = [...selectedUsers.value, record];
        } else {
          selectedUsers.value = selectedUsers.value.filter(
            (user) => user.id !== record.id
          );
        }
      }
    },
  }));

  // 行点击处理
  const onRowClick = (record: User) => {
    console.log(record);
    const isSelected = selectedUsers.value.some(
      (user) => user.id === record.id
    );
    if (!props.multiple) {
      selectedUsers.value = isSelected ? [] : [record];
    } else {
      if (isSelected) {
        selectedUsers.value = selectedUsers.value.filter(
          (user) => user.id !== record.id
        );
      } else {
        selectedUsers.value = [...selectedUsers.value, record];
      }
    }
  };

  const query = useApiTrpc().admin.user.queryUser.query;

  // 初始化选中状态
  onMounted(() => {
    if (value.value) {
      selectedUsers.value = Array.isArray(value.value)
        ? [...value.value]
        : [value.value];
    }
  });

  // 显示模态框
  const showModal = () => {
    isOpen.value = true;
  };

  // 搜索处理
  const onSearch = (value: string) => {
    searchQuery.value = value;
  };

  // 取消选择
  const handleCancel = () => {
    isOpen.value = false;
  };

  // 确认选择
  const handleConfirm = () => {
    const result = props.multiple
      ? selectedUsers.value
      : selectedUsers.value[0];
    value.value = result;
    emit("selected", result);
    isOpen.value = false;
  };
</script>

<style scoped></style>
