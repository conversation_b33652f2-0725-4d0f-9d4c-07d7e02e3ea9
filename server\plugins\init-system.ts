import { SeedService } from "~/server/services/seed.service";
import { prisma } from "~/lib/prisma";

export default defineNitroPlugin(async (nitroApp) => {
  try {
    // 检查系统是否已初始化
    const system = await prisma.system.findFirst();
    if (!system) {
      console.log("首次运行，开始初始化系统...");
      // 同步所有基础数据
      await SeedService.syncAll();
      console.log("系统初始化完成");
    } else {
      console.log(`系统已于${system.createAt.toLocaleString()}安装`);
    }
  } catch (error) {
    console.error("系统初始化失败:", error);
  }
});
