import { createNuxtApiHand<PERSON> } from "trpc-nuxt";
import { appRouter as router } from "~/server/trpc/routers";
import { createContext } from "~/server/trpc/context";

// export API handler
export default createNuxtApiHandler({
  router,
  createContext,
  /**
   * 错误处理
   * @param opts 
   * {
      error: TRPCError; // the original error
      type: 'query' | 'mutation' | 'subscription' | 'unknown';
      path: string | undefined; // path of the procedure that was triggered
      input: unknown;
      ctx: Context | undefined;
      req: BaseRequest; // request object
    }
   */
  onError: (opts) => {
    const { error, type, path, input, ctx, req } = opts;
    const consoleErrorCode: (typeof error.code)[] = [
      "INTERNAL_SERVER_ERROR",
      "NOT_IMPLEMENTED",
      "BAD_REQUEST",
    ];
    if (consoleErrorCode.includes(error.code)) {
      console.error("Error:", error);
    }
  },
});
