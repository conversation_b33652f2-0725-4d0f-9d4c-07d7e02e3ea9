<template>
  <ui-storemanage-frame
    v-model:breadcrumb="breadcrumb"
    @select-menu-item="handleSelectMenuItem"
    @logout="logout"
  >
    <template #menuItem>
      <a-menu-item key="0">首页</a-menu-item>
      <a-menu-item-group title="生产管理">
        <a-menu-item key="productiontask">生产工单</a-menu-item>
        <a-menu-item key="warehousemgr">生产报工</a-menu-item>
        <a-menu-item key="disinfection">消毒处理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="质量管理">
        <a-menu-item key="materielmgr">常见缺陷</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="排班管理">
        <a-menu-item key="materielmgr">人员管理</a-menu-item>
        <a-menu-item key="materielmgr">班组管理</a-menu-item>
      </a-menu-item-group>
      <a-menu-item-group title="报表管理">
        <a-menu-item key="materielmgr">员工绩效</a-menu-item>
        <a-menu-item key="materielmgr">产量统计</a-menu-item>
      </a-menu-item-group>
    </template>
    <nuxt-page></nuxt-page>
  </ui-storemanage-frame>
</template>
<script lang="ts" setup>
  useHead({
    title: "UAR管理系统-生产管理",
  });
  const { $emitter } = useNuxtApp();
  const breadcrumb = ref(["首页"]);
  const test = () => {
    $emitter.emit("changeLoginBoxStatus", true);
  };
  const handleSelectMenuItem = (e: any) => {
    switch (e.key) {
      // 首页
      case "0":
        breadcrumb.value = ["首页"];
        navigateTo("/storemanage/production");
        break;
      case "productiontask":
        breadcrumb.value = ["首页", "生产工单"];
        navigateTo("/storemanage/production/task");
        break;
      case "disinfection":
        breadcrumb.value = ["首页", "消毒处理"];
        navigateTo("/manage/mes/production/disinfection");
        break;
    }
  };
  const logout = () => {
    Modal.confirm({
      title: "提示",
      content: "确定要退出登录吗？",
      onOk: async () => {
        const exit = await useApiFetch.logout();
        if (exit.code) {
          navigateTo("/storemanage");
        }
      },
    });
  };
</script>
